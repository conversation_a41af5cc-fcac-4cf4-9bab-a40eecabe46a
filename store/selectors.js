import { createSelector } from "reselect";

const getState = (state) => state;

export const getStories = createSelector(getState, (state) => state.stories);

export const getCategories = createSelector(
  getState,
  (state) => state.categories
);

export const getSubCategories = createSelector(
  getState,
  (state) => state.subcategories
);

export const getBooks = createSelector(getState, (state) => state.books);

export const getSettings = createSelector(getState, (state) => state.settings);
