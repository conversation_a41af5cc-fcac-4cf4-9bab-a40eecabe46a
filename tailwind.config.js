module.exports = {
  purge: ["./pages/**/*.{js,ts,jsx,tsx}", "./components/**/*.{js,ts,jsx,tsx}"],
  darkMode: false, // or 'media' or 'class'
  theme: {
    boxShadow: {
      sm: "0 1px 2px 0 rgba(0, 0, 0, 0.05)",
      DEFAULT:
        "0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06)",
      md: "0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)",
      lg: "0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)",
      xl: "0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)",
      "2xl": "0 25px 50px -12px rgba(0, 0, 0, 0.25)",
      "3xl": "0 35px 60px -15px rgba(0, 0, 0, 0.3)",
      inner: "inset 0 2px 4px 0 rgba(0, 0, 0, 0.06)",
      none: "none",
      card: "0 4px 50px -10px rgba(0, 0, 0, 0.1), 0 2px 4px 4px rgba(0, 0, 0, 0.08)",
    },
    textColor: (theme) => theme("colors"),
    textColor: {
      primary: "#2D519F",
      secondary: "#43A6DD",
      disabled: "#92949c",
      white: "#ffffff",
      error: "#FF0000",
      greyDark: "#383a3e",
      greyMedium: "#808289",
      greylight: "#9d9fa6",
    },
    fontFamily: {
      PTSans: ["PT Sans", "sans-serif"],
    },

    extend: {},
  },
  variants: {
    extend: {},
  },
  plugins: [],
};
