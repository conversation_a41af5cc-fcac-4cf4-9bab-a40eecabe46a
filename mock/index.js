export const books = [
  {
    name: "Jane gets fit and healthy",
    id: "jane-gets-fit-and-heathly",
    tags: "Diet and lifestyle advice",
    cover_img: "/img/jane/jane-thumb.jpg",
    book_img: "/img/jane/jane-book.png",
    pages: [
      {
        info: "<PERSON> goes to the sports centre several times a week.",
        image: "/img/jane/1.jpg",
        index: 1,
      },
      {
        info: "She loves swimming.",
        image: "/img/jane/2.jpg",
        index: 2,
      },
      {
        info: "<PERSON> says hello to her friends.",
        image: "/img/jane/3.jpg",
        index: 3,
      },
      {
        info: "One of them offers her a cigarette. She says no",
        image: "/img/jane/4.jpg",
        index: 4,
      },
      {
        info: "Jane watches her weight.",
        image: "/img/jane/5.jpg",
        index: 5,
      },
      {
        info: "<PERSON> and her family go off to the country.",
        image: "/img/jane/6.jpg",
        index: 6,
      },
      {
        info: "Jane celebrates her 60th birthday with a picnic.",
        image: "/img/jane/7.jpg",
        index: 7,
      },
    ],
  },
];

export const stories = [
  {
    name: "HEALTH",
    id: "health",
    image: "/img/thumb-1.jpg",
    items: [
      {
        name: "Access to health care",
        id: "access-to-health-care",
        url: "categories",
      },
      {
        name: "Reasonable adjustments (health care)",
        id: "reasonable-adjusments",
      },
    ],
  },
  {
    name: "BEHAVIOUR",
    image: "/img/thumb-2.jpg",
  },
  {
    name: "HEALTHY LIVING",
    image: "/img/thumb-3.jpg",
  },
  {
    name: "CRIMINAL JUSTICE",
    image: "/img/thumb-4.jpg",
  },
  {
    name: "YOUNG PEOPLE",
    image: "/img/thumb-5.jpg",
  },
  {
    name: "ABUSE AND TRAUMA",
    image: "/img/thumb-6.jpg",
  },
  {
    name: "ACCESSING SERVICES",
    image: "/img/thumb-7.jpg",
  },
  {
    name: "WORK, LIFESTYLE AND RELATIONSHIPS",
    image: "/img/thumb-8.jpg",
  },
  {
    name: "MENTAL HEALTH AND GRIEF",
    image: "/img/thumb-9.jpg",
  },
];

export const categories = [
  {
    name: "Diet and lifestyle advice",
    id: "diet-lifestyle-advice",
  },
  {
    name: "Reasonable adjustments (health care)",
    id: "reasonable-adjustments",
  },
  {
    name: "Sexual health care and information",
    id: "sexual-health",
  },
];

export const subcategories = [
  {
    name: "Diet and lifestyle advice",
    id: "diet-lifestyle-advice",
    items: [
      {
        name: "Jane gets fit and healthy",
        id: "jane-fit-and-healthy",
        image: "/img/jane/jane-thumb.jpg",
      },
      {
        name: "Rose has her weight checked",
        id: "rose-has-weight-checked",
        image: "/img/rose-thumb.jpg",
      },
    ],
  },
];
