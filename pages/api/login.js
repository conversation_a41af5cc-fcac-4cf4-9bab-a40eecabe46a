import fetchJson from "@lib/fetchJson";
import withSession from "@lib/session";
import { loginAxios } from "@utils/login";

export default withSession(async (req, res) => {
  const { email, password } = await req.body;

  // ** DEBUG ** //
  // console.log(email, password);
  // console.log(user);

  try {
    const user = await loginAxios()
      .post(`/api/user/login`, {
        mail: email,
        pass: password,
      })
      .then((res) => res.data)
      .then((data) => ({
        ...data.user,
        loginToken: data.jwt,
        message: data.message,
      }));

    if (user.message === "Change password!") {
      return res.json(user);
    }

    if (user.message === "Wrong email and/or password") {
      return res.status(401).json({ error });
    }

    if (user.message === "Email verification link sent") {
      return res.json(user);
    }

    if (user.id) {
      req.session.set("user", user);
      await req.session.save();
      res.json(user);
    }
    // console.log(res.json());
    // req.session.set("user", user);
    // await req.session.save();
    // res.json(user);
  } catch (error) {
    // console.log("error catch!");
    return res.status(401).json({ error });
  }
});
