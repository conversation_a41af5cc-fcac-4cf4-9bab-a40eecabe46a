import fetchJson from "@lib/fetchJson";
import withSession from "@lib/session";

import axios from "axios";

export default withSession(async (req, res) => {
  const { email, password } = await req.body;

  // console.log(email, password);

  try {
    const user = await axios.post(
      process.env.NEXT_PUBLIC_FIREBASE_API_URL + `/api/user`,
      {
        mail: email,
        pass: password,
      }
    );

    //console.log(user);

    if (user.data.message === "User exists") {
      // console.log(user.data.message);
      return res
        .status(200)
        .json({ message: user.data.message, userRegistered: false });
    } else {
      // console.log(user.data);
      return res
        .status(200)
        .json({ message: user.data.message, userRegistered: true });
      // return res.status(200);
    }

    // req.session.set("user", user);
    // await req.session.save();
    // res.json(user);
  } catch (error) {
    // console.log("error catch!");
    return res.status(500).json({ error });
  }
});
