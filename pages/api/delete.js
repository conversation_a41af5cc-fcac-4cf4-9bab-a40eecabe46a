import fetchJson from "@lib/fetchJson";
import withSession from "@lib/session";
import { loginAxios } from "@utils/login";

export default withSession(async (req, res) => {
  const { email, password } = await req.body;

  // ** DEBUG ** //
  // console.log(email, password);
  // console.log(user);

  try {
    const user = await loginAxios()
      .post(`/api/user/login`, {
        mail: email,
        pass: password,
      })
      .then((res) => res.data)
      .then((data) => {
        // console.log(data);

        // const user = await loginAxios().get(`/api/user/` + 33);
        return data;

        // try {
        //   const userDelete = await loginAxios().get(
        //     `/api/user/` + res.data.user.id
        //   );
        //   return res.status(200).json({ error });
        // } catch (error) {
        //   return res.status(200).json({ error });
        // }
      });
    // .then((res) => console.log(res.data.user.id));
    // .then((res) => console.log(res.data.user.id));

    // try {
    //   const userDelete = await loginAxios().get(
    //     `/api/user/` + res.data.user.id
    //   );
    //   return res.status(200).json({ error });
    // } catch (error) {
    //   return res.status(200).json({ error });
    // }

    return res.status(200).json({ user });
  } catch (error) {
    // console.log("error catch!");
    return res.status(401).json({ error });
  }
});
