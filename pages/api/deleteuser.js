import fetchJson from "@lib/fetchJson";
import withSession from "@lib/session";
import { loginAxios } from "@utils/login";

export default withSession(async (req, res) => {
  // console.log(req.body);
  const { id, token } = await req.body;

  try {
    const user = await loginAxios().delete(`/api/user/` + id, {
      headers: {
        Authorization: "Bearer " + token,
      },
    });
    return res.json(user.data);
  } catch (error) {
    return res.status(401).json({ error });
  }
});
