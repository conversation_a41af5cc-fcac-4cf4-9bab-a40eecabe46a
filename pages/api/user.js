import Cors from "cors";
import initMiddleware from "../../lib/init-middleware";
import withSession from "../../lib/session";

// Initialize the cors middleware
const cors = initMiddleware(
  // You can read more about the available options here: https://github.com/expressjs/cors#configuration-options
  Cors({
    // Only allow requests with GET, POST and OPTIONS
    methods: ["GET", "POST", "OPTIONS"],
  })
);

export default withSession(async (req, res) => {
  await cors(req, res);

  const user = req.session.get("user");

  if (user) {
    res.json({
      isLoggedIn: true,
      ...user,
    });
  } else {
    res.json({
      isLoggedIn: false,
    });
  }
});
