import fetchJson from "@lib/fetchJson";
import withSession from "@lib/session";
import { loginAxios } from "@utils/login";

export default withSession(async (req, res) => {
  const { email, password } = await req.body;

  try {
    const user = await loginAxios().post(`/api/user/reset-password`, {
      mail: email,
      pass: password,
    });

    return res.json(user.data);
  } catch (error) {
    return res.status(401).json({ error });
  }
});
