import { useState, useEffect } from "react";
import useUser from "@lib/useUser";
import TopNav from "@components/ui/TopNav";
import Link from "next/link";
import { ChevronRightIcon } from "@heroicons/react/solid";
import InfoView from "@components/ui/Info";
import { Capacitor } from "@capacitor/core";
import { supabase } from "@utils/supabaseClient";

export default function Scenario({ scenario, stories }) {
  const storiesCurr = scenario.stories;
  const [showInfo, setShowInfo] = useState(false);
  const [isApple, setIsApple] = useState(false);
  const { user, mutateUser } = useUser();
  const [userGroups, setUserGroups] = useState(false);
  const [session, setSession] = useState(null);
  const [whiteLabelUser, setWhiteLabelUser] = useState(null);
  const [userProfile, setUserProfile] = useState(null);
  const [loading, setLoading] = useState(true);

  // ** DEBUG ** //

  // console.log(stories);

  // console.log("userProfile");
  // console.log(userProfile);

  // console.log("whiteLabelUser");
  // console.log(whiteLabelUser);

  // ** DEBUG ** //

  // filter the required scenarios data
  // const storyArray = stories && stories.filter(function (item) {
  //   return storiesCurr.indexOf(item.legacy_id) !== -1;
  // });

  // chanmge the order
  const filteredStories =
    stories &&
    stories.sort(function (a, b) {
      var textA = a.name.toUpperCase();
      var textB = b.name.toUpperCase();
      return textA < textB ? -1 : textA > textB ? 1 : 0;
    });

  // console.log(filteredStories);

  useEffect(() => {
    supabase.auth.getSession().then(({ data: { session } }) => {
      setSession(session);
    });

    supabase.auth.onAuthStateChange((_event, session) => {
      setSession(session);
    });
  }, []);

  useEffect(() => {
    let mounted = true;
    if (Capacitor.getPlatform() === "ios") {
      setIsApple(true);
    }
    getUserProfile();
    return function cleanup() {
      mounted = false;
    };
  }, [session]);

  const toggleInfo = () => {
    setShowInfo(!showInfo);
  };

  useEffect(() => {
    if (Capacitor.getPlatform() === "ios") {
      setIsApple(true);
    }
  }, []);

  async function getUserProfile() {
    try {
      setLoading(true);

      const {
        data: { user },
      } = await supabase.auth.getUser();

      let { data, error, status } = await supabase
        .from("profiles")
        .select("*")
        .eq("id", user.id)
        .single();

      if (error && status !== 406) {
        throw error;
      }

      if (data) {
        setUserProfile(data);
        // setWhiteLabelUser(data.active);

        // check date

        const userExpDate = data.white_label;

        if (userExpDate !== null) {
          const date = new Date();

          let day = date.getDate();
          let month = date.getMonth() + 1;
          let year = date.getFullYear();

          let currentDateTmp = `${year}-${month}-${day}`;
          const currentDate = currentDateTmp;

          const userExpDateParse = Date.parse(userExpDate);
          const currDateParse = Date.parse(currentDate);

          // if the user has white label access set the flag here (valid date)
          if (userExpDateParse < currDateParse) {
            setWhiteLabelUser(false);
          } else {
            setWhiteLabelUser(true);
          }
        } else {
          setDisplayWhiteLabel(false);
        }
      }
    } catch (error) {
      // console.log("user not logged in");
    } finally {
      setLoading(false);
    }
  }

  // Add user started_reading_story

  async function addUserEvent(name) {
    try {
      setLoading(true);

      const dataEnter = {
        user_id: userProfile.id,
        event_type: "started_reading_story_" + name,
        organisation: userProfile.organisation,
        profession: userProfile.profession,
        country: userProfile.country,
      };

      const { data, error } = await supabase
        .from("events")
        .insert(dataEnter)
        .select();

      if (error) {
        console.log(error);
        throw error;
      }
    } catch (error) {
      console.log(error.message);
    } finally {
      // trigger created_account event
      setLoading(false);
    }
  }

  return (
    <div className="flex flex-col h-screen">
      {showInfo ? (
        <InfoView
          info={scenario.info}
          toggleInfo={toggleInfo}
          title={scenario.name}
          iOS={isApple}
        />
      ) : (
        <>
          <TopNav title={scenario.name} toggleInfo={toggleInfo} iOS={isApple} />
          <main className="flex-1 overflow-y-auto">
            <div>
              <ul className="divide-y divide-gray-200">
                {filteredStories &&
                  filteredStories.map((story, index) => (
                    <li
                      key={story.slug}
                      onClick={() => addUserEvent(story.name)}
                    >
                      {story.free || whiteLabelUser ? (
                        <Link key={index} href={"/story/" + story.slug}>
                          <div
                            className={
                              story.free
                                ? "flex items-center px-4 py-4 sm:px-6"
                                : "flex items-center px-4 py-4 sm:px-6 opacity"
                            }
                          >
                            <div className="min-w-0 flex-1 flex items-center">
                              <div className="flex-shrink-0 aspect-w-10 rounded-md border shadow-lg overflow-hidden">
                                {story.thumbnail && (
                                  <img
                                    className="w-24 rounded-md"
                                    src={`data:image/jpeg;base64,${story.thumbnail}`}
                                  />
                                )}
                              </div>
                              <div className="min-w-0 flex-1 px-4 md:grid md:grid-cols-2 md:gap-4">
                                <div>
                                  <p className="font-medium text-greyDark opacity-90 ">
                                    {story.name}
                                  </p>
                                </div>
                              </div>
                            </div>
                            <div>
                              <ChevronRightIcon
                                className="h-5 w-5 text-greyDark opacity-90"
                                aria-hidden="true"
                              />
                            </div>
                          </div>
                        </Link>
                      ) : (
                        <div
                          className={
                            story.free
                              ? "flex items-center px-4 py-4 sm:px-6"
                              : "flex items-center px-4 py-4 sm:px-6 opacity-40"
                          }
                        >
                          <div className="min-w-0 flex-1 flex items-center">
                            <div className="flex-shrink-0 aspect-w-10 rounded-md border shadow-lg overflow-hidden">
                              {story.thumbnail && (
                                <img
                                  className="w-24 rounded-md"
                                  src={`data:image/jpeg;base64,${story.thumbnail}`}
                                />
                              )}
                            </div>
                            <div className="min-w-0 flex-1 px-4 md:grid md:grid-cols-2 md:gap-4">
                              <div>
                                <p className="font-medium text-greyDark opacity-90 ">
                                  {story.name}
                                </p>
                              </div>
                            </div>
                          </div>
                          <div>
                            <ChevronRightIcon
                              className="h-5 w-5 text-greyDark opacity-90"
                              aria-hidden="true"
                            />
                          </div>
                        </div>
                      )}
                    </li>
                  ))}
              </ul>
            </div>
          </main>
        </>
      )}
    </div>
  );
}

export async function getStaticPaths() {
  // const res = await fetch(
  //   process.env.NEXT_PUBLIC_FIREBASE_API_URL + "/api/scenario"
  // );

  // const scenarios = await res.json();

  // const paths = scenarios.map((scn) => ({
  //   params: { slug: scn.slug },
  // }));

  const { data, error } = await supabase.from("scenarios").select("*");

  const scenarios = data;

  const paths = scenarios.map((cat) => ({
    params: { slug: cat.slug },
  }));

  return {
    paths,
    fallback: false,
  };
}

export async function getStaticProps({ params }) {
  const { slug } = params;

  let scenario;
  let stories;
  let storyIds;
  let storyIdsArray;

  try {
    const { data, error } = await supabase
      .from("scenarios")

      .select("*")
      .eq("slug", slug)
      .single();

    scenario = data;

    if (data) {
      storyIds = JSON.parse(data.stories);
      // console.log(storyIds);
    }
  } catch (error) {
    console.log(error);
  } finally {
    /// filtered version

    try {
      const { data, error } = await supabase
        .from("stories")
        .select("*")
        .in("legacy_id", [storyIds]);
      // .contains('legacy_id', ["-Mf26090A7GPNeiHGEt1"]);
      //  .overlaps('legacy_id', ["-Mf26090A7GPNeiHGEt1"]);
      // .textSearch("legacy_id", [imageIds.ID]); // just need the write query here!
      stories = data;

      if (data) {
        // console.log(data);
        // console.log(imageIdsArray)
      }
    } catch (error) {
      console.log(error);
    } finally {
    }
  }

  //   }
  // here get the scenarios
  //   const { data, error } = await supabase.from("stories").select("*");
  //   stories = data;
  // }

  // const res = await fetch(
  //   process.env.NEXT_PUBLIC_FIREBASE_API_URL + `/api/scenario/${slug}`
  // );

  // const scenario = await res.json();

  return {
    props: { scenario, stories },
  };
}
