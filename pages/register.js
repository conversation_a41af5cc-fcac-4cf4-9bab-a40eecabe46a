import React, { useState, useEffect } from "react";
import Header from "@components/ui/Header";
import BottomNav from "@components/ui/BottomNav";
import { Capacitor } from "@capacitor/core";
import Link from "next/link";
import { Oval } from "svg-loaders-react";
import { supabase } from "@utils/supabaseClient";
// import RegisterForm from "@components/ui/RegisterForm";

const Register = () => {
  const [showInfo, setShowInfo] = useState(false);
  const [showUserReg, setShowUserReg] = useState(false);
  const [isApple, setIsApple] = useState(false);

  const [loading, setLoading] = useState(false);
  const [userData, setUserData] = useState(null);

  // const [name, setName] = useState({
  //   entry: "",
  //   type: "name",
  // });

  const [title, setTitle] = useState({
    entry: "",
    type: "title",
  });

  const [firstName, setFirstName] = useState({
    entry: "",
    type: "firstname",
  });

  const [lastName, setLastName] = useState({
    entry: "",
    type: "lastname",
  });

  const [organisation, setOrganisation] = useState({
    entry: "",
    type: "organisation",
  });

  const [profession, setProfession] = useState({
    entry: "",
    type: "profession",
  });

  const [country, setCountry] = useState({
    entry: "",
    type: "country",
  });

  const [email, setEmail] = useState([]);
  // const [password, setPassword] = useState([]);
  const [userID, setUserID] = useState(null);

  const [userCreated, setUserCreated] = useState(false);
  const [errorOnSubmit, setErrorOnSubmit] = useState("");

  /* --- DEBUG --- */

  // console.log("profession");
  // console.log(profession);

  // console.log(email);

  // console.log(password);

  // console.log("userID");
  // console.log(userID);

  // console.log("errorOnSubmit");
  // console.log(errorOnSubmit);

  /* --- DEBUG --- */

  useEffect(() => {
    let mounted = true;

    if (Capacitor.getPlatform() === "ios") {
      setIsApple(true);
    }

    return function cleanup() {
      mounted = false;
    };
  }, []);

  // validate form

  const validateForm = () => {
    // check name isn't empty

    let strTitle = title.entry;
    let strFirstName = firstName.entry;
    let strLastName = lastName.entry;
    let strEmail = email.entry;
    let strProfession = profession.entry;
    let strCountry = country.entry;

    // console.log("strProfession");
    // console.log(strProfession);

    if (
      strTitle.trim().length === 0 ||
      strFirstName.trim().length === 0 ||
      strLastName.trim().length === 0 ||
      strEmail.trim().length === 0 ||
      strProfession.trim().length === 0 ||
      strCountry.trim().length === 0
    ) {
      setErrorOnSubmit("Please complete all the required fields");
      // display error
    } else {
      const emailValid = validateEmail(email.entry);

      if (emailValid) {
        setErrorOnSubmit("");
        // validatePass();
        addUser();
      } else {
        setErrorOnSubmit("email address not valid");
      }
    }
    // check password is at least 6

    // function validatePass() {
    //   if (password.entry.length >= 6) {
    //     addUser();
    //   } else {
    //     setErrorOnSubmit("Your password needs to be at least 6 characters");
    //   }
    // }
  };

  // add user

  async function addUser() {
    const userDataToSend = {
      title: title.entry,
      firstName: firstName.entry,
      lastName: lastName.entry,
      organisation: organisation.entry,
      profession: profession.entry,
      country: country.entry,
      email: email.entry,
      // password: password.entry,
    };

    try {
      setLoading(true);

      // const { data, error } = await supabase.auth.signUp({
      //   email: userDataToSend.email,
      //   password: userDataToSend.password,
      // });

      const { data, error } = await supabase.functions.invoke("add-user", {
        body: JSON.stringify({ entry: email.entry }),
      });

      if (error) console.log(error);

      //   if (error) setErrorOnSubmit("A user with that email already exists");

      if (data) {
        if (data.error) {
          console.log(data.error);
          setErrorOnSubmit(data.error);
        } else {
          setUserID(data.user.id);
          //   console.log(data);
          // addUserAdmin(data);
          addUserProfile(data.user.id, userDataToSend);
        }
      }
    } catch (error) {
      // console.log("user not logged in");
    } finally {
      setLoading(false);
    }
  }

  async function addUserProfile(userID, userData) {
    try {
      setLoading(true);

      const dataEnter = {
        id: userID,
        title: userData.title,
        first_name: userData.firstName,
        last_name: userData.lastName,
        organisation: userData.organisation,
        profession: userData.profession,
        country: userData.country,
        email: userData.email,
      };

      // console.log(dataEnter);

      const { data, error } = await supabase
        .from("profiles")
        .insert(dataEnter)
        .select();

      if (error) {
        console.log(error);
        throw error;
      } else {
        setUserCreated(true);
      }
    } catch (error) {
      console.log(error.message);
      setErrorOnSubmit("User already exists");
      setUserCreated(false);
    } finally {
      // trigger created_account event
      // setLoading(false);
      addUserEvent(userID, userData);
      // console.log("record created");
    }
  }

  // Add user created_account event

  async function addUserEvent(userID, userData) {
    try {
      setLoading(true);

      const dataEnter = {
        user_id: userID,
        event_type: "created_account",
        organisation: userData.organisation,
        profession: userData.profession,
        country: userData.country,
      };

      //  console.log(dataEnter);

      const { data, error } = await supabase
        .from("events")
        .insert(dataEnter)
        .select();

      if (error) {
        console.log(error);
        throw error;
      }
    } catch (error) {
      console.log(error.message);
      // setErrorOnSubmit("connection error");
    } finally {
      // trigger created_account event
      setLoading(false);
      // console.log("event added");
    }
  }

  // validate email
  const validateEmail = (email) => {
    return String(email)
      .toLowerCase()
      .match(
        /^(([^<>()[\]\\.,;:\s@"]+(\.[^<>()[\]\\.,;:\s@"]+)*)|.(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/
      );
  };

  // update currSelection when entry changes
  const updateSelection = (e) => {
    const { value: value } = e.target;
    const type = e.target.id;

    // if (e.target.value !== "") {
    switch (type) {
      // case "name":
      //   setName({
      //     entry: value,
      //     type: type,
      //   });

      //   return;

      case "title":
        setTitle({
          entry: value,
          type: type,
        });

        return;

      case "firstname":
        setFirstName({
          entry: value,
          type: type,
        });

        return;

      case "lastname":
        setLastName({
          entry: value,
          type: type,
        });

        return;

      case "organisation":
        setOrganisation({
          entry: value,
          type: type,
        });

        return;

      case "profession":
        setProfession({
          entry: value,
          type: type,
        });

        return;

      case "country":
        setCountry({
          entry: value,
          type: type,
        });

        return;

      case "email":
        setEmail({
          entry: value,
          type: type,
        });

        return;

      // case "password":
      //   setPassword({
      //     entry: value,
      //     type: type,
      //   });

      //   return;

      default:
        return;
    }
    // }
  };

  const toggleInfo = () => {
    setShowInfo(!showInfo);
  };

  return (
    <>
      {loading ? (
        <div className="grid h-screen place-items-center">
          <div className="mt-10 mb-10 flex flex-row space-x-2">
            <Oval stroke="#0c39ac" />
          </div>
        </div>
      ) : (
        <>
          <div className="flex flex-col h-screen">
            <Header iOS={isApple} toggleInfo={toggleInfo} info={true} />

            <main className="flex-1 overflow-y-auto bg-gray-100 ">
              {userCreated ? (
                <div className="flex flex-col justify-center py-12 sm:px-6 lg:px-8 p-3">
                  <div>
                    <h2 className="font-Montserrat mt-6 text-center text-2xl font-extrabold text-gray-800">
                      Your account has been created
                    </h2>
                    <p className="font-Montserrat mt-6 text-center text-gray-800">
                      Please check your email to verify your email to verify
                      your email address before logging in.
                    </p>
                  </div>
                  <div className="mt-6 p-6">
                    <Link href="/login">
                      <button className=" bg-blue-800 w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-secondary focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                        Login
                      </button>
                    </Link>
                  </div>
                </div>
              ) : (
                // <RegisterForm errorMessage={errorMsg} />
                <div className="bg-gray-100 flex flex-col justify-center py-6 sm:px-6 lg:px-8">
                  <div className="sm:mx-auto sm:w-full sm:max-w-md">
                    <div>
                      <h2 className="font-Montserrat mt-0 text-center text-2xl font-extrabold text-gray-800 p-3">
                        Create an account
                      </h2>
                    </div>
                  </div>

                  <div className="mt-3 sm:mx-auto sm:w-full sm:max-w-md m-6">
                    <div className="bg-white py-8 px-4 shadow sm:rounded-lg sm:px-10">
                      {/* <div className="pb-3">
                        <label
                          htmlFor="name"
                          className="block text-sm font-medium text-gray-700"
                        >
                          Name
                        </label>
                        <div className="mt-1">
                          <input
                            value={(name && name.entry) || ""}
                            onChange={updateSelection}
                            id="name"
                            name="name"
                            type="text"
                            className="appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                          />
                        </div>
                      </div> */}
                      <div className="pb-3">
                        <label
                          htmlFor="title"
                          className="block text-sm font-medium text-gray-700"
                        >
                          Title *
                        </label>
                        <div className="mt-1">
                          <input
                            value={(title && title.entry) || ""}
                            onChange={updateSelection}
                            id="title"
                            name="title"
                            type="text"
                            className="appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                          />
                        </div>
                      </div>
                      <div className="pb-3">
                        <label
                          htmlFor="firstname"
                          className="block text-sm font-medium text-gray-700"
                        >
                          First name *
                        </label>
                        <div className="mt-1">
                          <input
                            value={(firstName && firstName.entry) || ""}
                            onChange={updateSelection}
                            id="firstname"
                            name="firstname"
                            type="text"
                            className="appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                          />
                        </div>
                      </div>
                      <div className="pb-3">
                        <label
                          htmlFor="lastName"
                          className="block text-sm font-medium text-gray-700"
                        >
                          Last name *
                        </label>
                        <div className="mt-1">
                          <input
                            value={(lastName && lastName.entry) || ""}
                            onChange={updateSelection}
                            id="lastname"
                            name="lastname"
                            type="text"
                            className="appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                          />
                        </div>
                      </div>
                      <div className="pb-3">
                        <label
                          htmlFor="email"
                          className="block text-sm font-medium text-gray-700"
                        >
                          Email *
                        </label>
                        <div className="mt-1">
                          <input
                            value={(email && email.entry) || ""}
                            onChange={updateSelection}
                            id="email"
                            name="email"
                            type="email"
                            className="appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                          />
                        </div>
                      </div>
                      <div className="pb-3">
                        <label
                          htmlFor="organisation"
                          className="block text-sm font-medium text-gray-700"
                        >
                          Organisation
                        </label>
                        <div className="mt-1">
                          <input
                            value={(organisation && organisation.entry) || ""}
                            onChange={updateSelection}
                            id="organisation"
                            name="organisation"
                            type="text"
                            className="appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                          />
                        </div>
                      </div>
                      <div className="pb-3">
                        <label
                          htmlFor="profession"
                          className="block text-sm font-medium text-gray-700"
                        >
                          Profession *
                        </label>
                        <div className="mt-1">
                          <select
                            required
                            id="profession"
                            name="profession"
                            onChange={(event) =>
                              setProfession({
                                entry: event.target.value,
                                type: "profession",
                              })
                            }
                            className="border mt-1  w-56 py-2 text-base border-gray-300 focus:outline-none focus:ring-petrolBlue focus:border-petrolBlue sm:text-sm rounded-md"
                          >
                            <option defaultValue />
                            <option value="Dentist">Dentist</option>
                            <option value="GP">GP</option>
                            <option value="Occupational Therapist">
                              Occupational Therapist
                            </option>
                            <option value="LD Nurse">LD Nurse</option>
                            <option value="Mental Health Professional">
                              Mental Health Professional
                            </option>
                            <option value="Special Needs Education">
                              Special Needs Education
                            </option>
                            <option value="Librarian">Librarian</option>
                            <option value="Support Worker">
                              Support Worker
                            </option>
                            <option value="Eyesight Professional">
                              Eyesight Professional
                            </option>
                            <option value="Family Carer">Family Carer</option>
                            <option value="Care Commissioning Group">
                              Care Commissioning Group
                            </option>
                            <option value="Criminal Justice">
                              Criminal Justice
                            </option>
                            <option value="Local Authority">
                              Local Authority
                            </option>
                            =
                          </select>
                        </div>
                      </div>
                      <div className="pb-3">
                        <label
                          htmlFor="country"
                          className="block text-sm font-medium text-gray-700"
                        >
                          Country *
                        </label>
                        <div className="mt-1">
                          <select
                            required
                            id="country"
                            name="country"
                            onChange={(event) =>
                              setCountry({
                                entry: event.target.value,
                                type: "country",
                              })
                            }
                            className="border mt-1  w-56 py-2 text-base border-gray-300 focus:outline-none focus:ring-petrolBlue focus:border-petrolBlue sm:text-sm rounded-md"
                          >
                            <option defaultValue />
                            <option value="United Kingdom">
                              United Kingdom
                            </option>
                            <option value="Afghanistan">Afghanistan</option>
                            <option value="Åland Islands">Åland Islands</option>
                            <option value="Albania">Albania</option>
                            <option value="Algeria">Algeria</option>
                            <option value="American Samoa">
                              American Samoa
                            </option>
                            <option value="Andorra">Andorra</option>
                            <option value="Angola">Angola</option>
                            <option value="Anguilla">Anguilla</option>
                            <option value="Antarctica">Antarctica</option>
                            <option value="Antigua and Barbuda">
                              Antigua and Barbuda
                            </option>
                            <option value="Argentina">Argentina</option>
                            <option value="Armenia">Armenia</option>
                            <option value="Aruba">Aruba</option>
                            <option value="Australia">Australia</option>
                            <option value="Austria">Austria</option>
                            <option value="Azerbaijan">Azerbaijan</option>
                            <option value="Bahamas">Bahamas</option>
                            <option value="Bahrain">Bahrain</option>
                            <option value="Bangladesh">Bangladesh</option>
                            <option value="Barbados">Barbados</option>
                            <option value="Belarus">Belarus</option>
                            <option value="Belgium">Belgium</option>
                            <option value="Belize">Belize</option>
                            <option value="Benin">Benin</option>
                            <option value="Bermuda">Bermuda</option>
                            <option value="Bhutan">Bhutan</option>
                            <option value="Bolivia">Bolivia</option>
                            <option value="Bosnia and Herzegovina">
                              Bosnia and Herzegovina
                            </option>
                            <option value="Botswana">Botswana</option>
                            <option value="Bouvet Island">Bouvet Island</option>
                            <option value="Brazil">Brazil</option>
                            <option value="British Indian Ocean Territory">
                              British Indian Ocean Territory
                            </option>
                            <option value="Brunei Darussalam">
                              Brunei Darussalam
                            </option>
                            <option value="Bulgaria">Bulgaria</option>
                            <option value="Burkina Faso">Burkina Faso</option>
                            <option value="Burundi">Burundi</option>
                            <option value="Cambodia">Cambodia</option>
                            <option value="Cameroon">Cameroon</option>
                            <option value="Canada">Canada</option>
                            <option value="Cape Verde">Cape Verde</option>
                            <option value="Cayman Islands">
                              Cayman Islands
                            </option>
                            <option value="Central African Republic">
                              Central African Republic
                            </option>
                            <option value="Chad">Chad</option>
                            <option value="Chile">Chile</option>
                            <option value="China">China</option>
                            <option value="Christmas Island">
                              Christmas Island
                            </option>
                            <option value="Cocos (Keeling) Islands">
                              Cocos (Keeling) Islands
                            </option>
                            <option value="Colombia">Colombia</option>
                            <option value="Comoros">Comoros</option>
                            <option value="Congo">Congo</option>
                            <option value="Congo, The Democratic Republic of The">
                              Congo, The Democratic Republic of The
                            </option>
                            <option value="Cook Islands">Cook Islands</option>
                            <option value="Costa Rica">Costa Rica</option>
                            <option value="Cote Divoire">
                              Cote D&lsquo;ivoire
                            </option>
                            <option value="Croatia">Croatia</option>
                            <option value="Cuba">Cuba</option>
                            <option value="Cyprus">Cyprus</option>
                            <option value="Czech Republic">
                              Czech Republic
                            </option>
                            <option value="Denmark">Denmark</option>
                            <option value="Djibouti">Djibouti</option>
                            <option value="Dominica">Dominica</option>
                            <option value="Dominican Republic">
                              Dominican Republic
                            </option>
                            <option value="Ecuador">Ecuador</option>
                            <option value="Egypt">Egypt</option>
                            <option value="El Salvador">El Salvador</option>
                            <option value="Equatorial Guinea">
                              Equatorial Guinea
                            </option>
                            <option value="Eritrea">Eritrea</option>
                            <option value="Estonia">Estonia</option>
                            <option value="Ethiopia">Ethiopia</option>
                            <option value="Falkland Islands (Malvinas)">
                              Falkland Islands (Malvinas)
                            </option>
                            <option value="Faroe Islands">Faroe Islands</option>
                            <option value="Fiji">Fiji</option>
                            <option value="Finland">Finland</option>
                            <option value="France">France</option>
                            <option value="French Guiana">French Guiana</option>
                            <option value="French Polynesia">
                              French Polynesia
                            </option>
                            <option value="French Southern Territories">
                              French Southern Territories
                            </option>
                            <option value="Gabon">Gabon</option>
                            <option value="Gambia">Gambia</option>
                            <option value="Georgia">Georgia</option>
                            <option value="Germany">Germany</option>
                            <option value="Ghana">Ghana</option>
                            <option value="Gibraltar">Gibraltar</option>
                            <option value="Greece">Greece</option>
                            <option value="Greenland">Greenland</option>
                            <option value="Grenada">Grenada</option>
                            <option value="Guadeloupe">Guadeloupe</option>
                            <option value="Guam">Guam</option>
                            <option value="Guatemala">Guatemala</option>
                            <option value="Guernsey">Guernsey</option>
                            <option value="Guinea">Guinea</option>
                            <option value="Guinea-bissau">Guinea-bissau</option>
                            <option value="Guyana">Guyana</option>
                            <option value="Haiti">Haiti</option>
                            <option value="Heard Island and Mcdonald Islands">
                              Heard Island and Mcdonald Islands
                            </option>
                            <option value="Holy See (Vatican City State)">
                              Holy See (Vatican City State)
                            </option>
                            <option value="Honduras">Honduras</option>
                            <option value="Hong Kong">Hong Kong</option>
                            <option value="Hungary">Hungary</option>
                            <option value="Iceland">Iceland</option>
                            <option value="India">India</option>
                            <option value="Indonesia">Indonesia</option>
                            <option value="Iran, Islamic Republic of">
                              Iran, Islamic Republic of
                            </option>
                            <option value="Iraq">Iraq</option>
                            <option value="Ireland">Ireland</option>
                            <option value="Isle of Man">Isle of Man</option>
                            <option value="Israel">Israel</option>
                            <option value="Italy">Italy</option>
                            <option value="Jamaica">Jamaica</option>
                            <option value="Japan">Japan</option>
                            <option value="Jersey">Jersey</option>
                            <option value="Jordan">Jordan</option>
                            <option value="Kazakhstan">Kazakhstan</option>
                            <option value="Kenya">Kenya</option>
                            <option value="Kiribati">Kiribati</option>
                            <option value="Korea, Democratic People's Republic of">
                              Korea, Democratic People&lsquo;s Republic of
                            </option>
                            <option value="Korea, Republic of">
                              Korea, Republic of
                            </option>
                            <option value="Kuwait">Kuwait</option>
                            <option value="Kyrgyzstan">Kyrgyzstan</option>
                            <option value="Lao People's Democratic Republic">
                              Lao People&lsquo;s Democratic Republic
                            </option>
                            <option value="Latvia">Latvia</option>
                            <option value="Lebanon">Lebanon</option>
                            <option value="Lesotho">Lesotho</option>
                            <option value="Liberia">Liberia</option>
                            <option value="Libyan Arab Jamahiriya">
                              Libyan Arab Jamahiriya
                            </option>
                            <option value="Liechtenstein">Liechtenstein</option>
                            <option value="Lithuania">Lithuania</option>
                            <option value="Luxembourg">Luxembourg</option>
                            <option value="Macao">Macao</option>
                            <option value="Macedonia, The Former Yugoslav Republic of">
                              Macedonia, The Former Yugoslav Republic of
                            </option>
                            <option value="Madagascar">Madagascar</option>
                            <option value="Malawi">Malawi</option>
                            <option value="Malaysia">Malaysia</option>
                            <option value="Maldives">Maldives</option>
                            <option value="Mali">Mali</option>
                            <option value="Malta">Malta</option>
                            <option value="Marshall Islands">
                              Marshall Islands
                            </option>
                            <option value="Martinique">Martinique</option>
                            <option value="Mauritania">Mauritania</option>
                            <option value="Mauritius">Mauritius</option>
                            <option value="Mayotte">Mayotte</option>
                            <option value="Mexico">Mexico</option>
                            <option value="Micronesia, Federated States of">
                              Micronesia, Federated States of
                            </option>
                            <option value="Moldova, Republic of">
                              Moldova, Republic of
                            </option>
                            <option value="Monaco">Monaco</option>
                            <option value="Mongolia">Mongolia</option>
                            <option value="Montenegro">Montenegro</option>
                            <option value="Montserrat">Montserrat</option>
                            <option value="Morocco">Morocco</option>
                            <option value="Mozambique">Mozambique</option>
                            <option value="Myanmar">Myanmar</option>
                            <option value="Namibia">Namibia</option>
                            <option value="Nauru">Nauru</option>
                            <option value="Nepal">Nepal</option>
                            <option value="Netherlands">Netherlands</option>
                            <option value="Netherlands Antilles">
                              Netherlands Antilles
                            </option>
                            <option value="New Caledonia">New Caledonia</option>
                            <option value="New Zealand">New Zealand</option>
                            <option value="Nicaragua">Nicaragua</option>
                            <option value="Niger">Niger</option>
                            <option value="Nigeria">Nigeria</option>
                            <option value="Niue">Niue</option>
                            <option value="Norfolk Island">
                              Norfolk Island
                            </option>
                            <option value="Northern Mariana Islands">
                              Northern Mariana Islands
                            </option>
                            <option value="Norway">Norway</option>
                            <option value="Oman">Oman</option>
                            <option value="Pakistan">Pakistan</option>
                            <option value="Palau">Palau</option>
                            <option value="Palestinian Territory, Occupied">
                              Palestinian Territory, Occupied
                            </option>
                            <option value="Panama">Panama</option>
                            <option value="Papua New Guinea">
                              Papua New Guinea
                            </option>
                            <option value="Paraguay">Paraguay</option>
                            <option value="Peru">Peru</option>
                            <option value="Philippines">Philippines</option>
                            <option value="Pitcairn">Pitcairn</option>
                            <option value="Poland">Poland</option>
                            <option value="Portugal">Portugal</option>
                            <option value="Puerto Rico">Puerto Rico</option>
                            <option value="Qatar">Qatar</option>
                            <option value="Reunion">Reunion</option>
                            <option value="Romania">Romania</option>
                            <option value="Russian Federation">
                              Russian Federation
                            </option>
                            <option value="Rwanda">Rwanda</option>
                            <option value="Saint Helena">Saint Helena</option>
                            <option value="Saint Kitts and Nevis">
                              Saint Kitts and Nevis
                            </option>
                            <option value="Saint Lucia">Saint Lucia</option>
                            <option value="Saint Pierre and Miquelon">
                              Saint Pierre and Miquelon
                            </option>
                            <option value="Saint Vincent and The Grenadines">
                              Saint Vincent and The Grenadines
                            </option>
                            <option value="Samoa">Samoa</option>
                            <option value="San Marino">San Marino</option>
                            <option value="Sao Tome and Principe">
                              Sao Tome and Principe
                            </option>
                            <option value="Saudi Arabia">Saudi Arabia</option>
                            <option value="Senegal">Senegal</option>
                            <option value="Serbia">Serbia</option>
                            <option value="Seychelles">Seychelles</option>
                            <option value="Sierra Leone">Sierra Leone</option>
                            <option value="Singapore">Singapore</option>
                            <option value="Slovakia">Slovakia</option>
                            <option value="Slovenia">Slovenia</option>
                            <option value="Solomon Islands">
                              Solomon Islands
                            </option>
                            <option value="Somalia">Somalia</option>
                            <option value="South Africa">South Africa</option>
                            <option value="South Georgia and The South Sandwich Islands">
                              South Georgia and The South Sandwich Islands
                            </option>
                            <option value="Spain">Spain</option>
                            <option value="Sri Lanka">Sri Lanka</option>
                            <option value="Sudan">Sudan</option>
                            <option value="Suriname">Suriname</option>
                            <option value="Svalbard and Jan Mayen">
                              Svalbard and Jan Mayen
                            </option>
                            <option value="Swaziland">Swaziland</option>
                            <option value="Sweden">Sweden</option>
                            <option value="Switzerland">Switzerland</option>
                            <option value="Syrian Arab Republic">
                              Syrian Arab Republic
                            </option>
                            <option value="Taiwan">Taiwan</option>
                            <option value="Tajikistan">Tajikistan</option>
                            <option value="Tanzania, United Republic of">
                              Tanzania, United Republic of
                            </option>
                            <option value="Thailand">Thailand</option>
                            <option value="Timor-leste">Timor-leste</option>
                            <option value="Togo">Togo</option>
                            <option value="Tokelau">Tokelau</option>
                            <option value="Tonga">Tonga</option>
                            <option value="Trinidad and Tobago">
                              Trinidad and Tobago
                            </option>
                            <option value="Tunisia">Tunisia</option>
                            <option value="Turkey">Turkey</option>
                            <option value="Turkmenistan">Turkmenistan</option>
                            <option value="Turks and Caicos Islands">
                              Turks and Caicos Islands
                            </option>
                            <option value="Tuvalu">Tuvalu</option>
                            <option value="Uganda">Uganda</option>
                            <option value="Ukraine">Ukraine</option>
                            <option value="United Arab Emirates">
                              United Arab Emirates
                            </option>
                            <option value="United Kingdom">
                              United Kingdom
                            </option>
                            <option value="United States">United States</option>
                            <option value="United States Minor Outlying Islands">
                              United States Minor Outlying Islands
                            </option>
                            <option value="Uruguay">Uruguay</option>
                            <option value="Uzbekistan">Uzbekistan</option>
                            <option value="Vanuatu">Vanuatu</option>
                            <option value="Venezuela">Venezuela</option>
                            <option value="Viet Nam">Viet Nam</option>
                            <option value="Virgin Islands, British">
                              Virgin Islands, British
                            </option>
                            <option value="Virgin Islands, U.S.">
                              Virgin Islands, U.S.
                            </option>
                            <option value="Wallis and Futuna">
                              Wallis and Futuna
                            </option>
                            <option value="Western Sahara">
                              Western Sahara
                            </option>
                            <option value="Yemen">Yemen</option>
                            <option value="Zambia">Zambia</option>
                            <option value="Zimbabwe">Zimbabwe</option>
                          </select>
                        </div>
                      </div>

                      {/* <div className="pb-0">
                        <label
                          htmlFor="password"
                          className="block text-sm font-medium text-gray-700"
                        >
                          Set your password
                        </label>
                        <div className="mt-1">
                          <input
                            value={(password && password.entry) || ""}
                            onChange={updateSelection}
                            id="password"
                            name="password"
                            type="password"
                            className="appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                          />
                        </div>
                      </div> */}

                      <div className="mt-6">
                        <button
                          // onClick={addUser}
                          onClick={validateForm}
                          className=" bg-green-500 w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-secondary focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                        >
                          Create account
                        </button>

                        {errorOnSubmit ? (
                          <div className="text-error pt-2">{errorOnSubmit}</div>
                        ) : null}
                      </div>
                    </div>
                  </div>
                </div>
              )}
            </main>
            <BottomNav name="account" />
          </div>
        </>
      )}
    </>
  );
};

export default Register;
