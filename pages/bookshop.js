import { useState, useEffect } from "react";
import BottomNav from "@components/ui/BottomNav";
import Header from "@components/ui/Header";
import { Capacitor } from "@capacitor/core";
import Card from "@components/ui/Card";
import Image from "next/image";
import Link from "next/link";
import WelcomeView from "@components/ui/Welcome";
import { supabase } from "@utils/supabaseClient";

export default function eBooks() {
  const [isApple, setIsApple] = useState(false);
  const [showInfo, setShowInfo] = useState(false);
  const [loading, setLoading] = useState(true);
  const [userGroups, setUserGroups] = useState(false);
  const [userProfile, setUserProfile] = useState(null);
  const [session, setSession] = useState(null);

  // console.log(userProfile);

  const toggleInfo = () => {
    setShowInfo(!showInfo);
  };

  useEffect(() => {
    let mounted = true;

    if (Capacitor.getPlatform() === "ios") {
      setIsApple(true);
    }

    setLoading(false);

    return function cleanup() {
      mounted = false;
    };
  }, []);

  useEffect(() => {
    supabase.auth.getSession().then(({ data: { session } }) => {
      setSession(session);
    });

    supabase.auth.onAuthStateChange((_event, session) => {
      setSession(session);
    });
  }, []);

  useEffect(() => {
    let mounted = true;
    // if (Capacitor.getPlatform() === "ios") {
    //   setIsApple(true);
    // }
    getUserProfile();
    return function cleanup() {
      mounted = false;
    };
  }, [session]);

  async function getUserProfile() {
    try {
      setLoading(true);

      const {
        data: { user },
      } = await supabase.auth.getUser();

      let { data, error, status } = await supabase
        .from("profiles")
        .select("*")
        .eq("id", user.id)
        .single();

      if (error && status !== 406) {
        throw error;
      }

      if (data) {
        setUserProfile(data);

        // console.log(data);
      }
    } catch (error) {
      // console.log("user not logged in");
    } finally {
      setLoading(false);
    }
  }

  // Add user started_reading_story

  async function addUserEvent(name) {
    try {
      setLoading(true);

      const dataEnter = {
        user_id: userProfile.id,
        event_type: "visited_bookstore",
        organisation: userProfile.organisation,
        profession: userProfile.profession,
        country: userProfile.country,
      };

      const { data, error } = await supabase
        .from("events")
        .insert(dataEnter)
        .select();

      if (error) {
        console.log(error);
        throw error;
      }
    } catch (error) {
      console.log(error.message);
    } finally {
      // trigger created_account event
      setLoading(false);
    }
  }

  return (
    <div className="flex flex-col h-screen">
      {showInfo ? (
        <WelcomeView
          toggleInfo={toggleInfo}
          // title={category.name}
          iOS={isApple}
        />
      ) : (
        <>
          <Header iOS={isApple} toggleInfo={toggleInfo} info={true} />
          <main className="flex-1 overflow-y-auto">
            {/* <div className="p-6" dangerouslySetInnerHTML={{ __html: content }} /> */}

            {/* <div className="pt-0 pl-3">
          <div>Bookshop</div>
        </div> */}

            {/* <Card className="my-3 mx-auto pl-3 pr-3"> */}
            <div className="pt-2 px-6 py-6 bg-white">
              <div className=" font-PTSans">
                {/* <p className="text-xl text-primary">Bookshop </p> */}

                <p>
                  Sometimes nothing beats holding a real book in your hands.
                </p>

                <div className="mx-auto flex justify-center">
                  <img
                    className="h-42 pl-0 pr-0 pt-2 pb-2"
                    src="/img/shop.jpg"
                  />
                </div>

                <p className=" pt-2 pb-2">
                  All of the stories in the Books Beyond Words series are
                  available in paperback.
                </p>

                <p>
                  Save money with tailored book sets designed for various
                  settings, including health care, education and community book
                  clubs.
                </p>

                <div className="mt-3">
                  <a
                    target="_blank"
                    href="https://booksbeyondwords.co.uk/bookshop"
                  >
                    <button
                      onClick={() => addUserEvent()}
                      className=" bg-blue-800 w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-secondary focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                    >
                      Visit our bookshop
                    </button>
                  </a>
                </div>
              </div>
            </div>

            {/* </Card> */}

            {/* <div className="p-6 font-PTSans">
          <p className="text-xl text-primary">Bookshop </p>

          <p>Sometimes nothing beats holding a real book in your hands.</p>

          <p>
            All of the stories in the Books Beyond Words series are available in
            paperback.
          </p>

          <p>
            Save money with tailored book sets designed for various settings,
            including health care, education and community book clubs.
          </p>

          <div className="mt-6">
            <a target="_blank" href="https://booksbeyondwords.co.uk/bookshop">
              <button className=" bg-blue-800 w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-secondary focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                Visit our bookshop
              </button>
            </a>
          </div>
        </div> */}
          </main>
        </>
      )}

      <BottomNav name="bookshop" showEbooks={userGroups} />
    </div>
  );
}
