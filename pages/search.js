import { useState, useEffect } from "react";
import Link from "next/link";
import Header from "@components/ui/Header";
import { Capacitor } from "@capacitor/core";
import { SearchIcon } from "@heroicons/react/solid";
import { ChevronRightIcon } from "@heroicons/react/solid";
import { supabase } from "@utils/supabaseClient";

export default function Search({ tags }) {
  // console.log(tags);

  // order tags name (a-z)

  const orderedTags = tags.sort(function (a, b) {
    var textA = a.name.toUpperCase();
    var textB = b.name.toUpperCase();
    return textA < textB ? -1 : textA > textB ? 1 : 0;
  });

  // console.log(orderedTags);

  const [showInfo, setShowInfo] = useState(false);
  const [isApple, setIsApple] = useState(false);
  const [searchInput, setSearchInput] = useState("");

  const toggleInfo = () => {
    setShowInfo(!showInfo);
  };

  useEffect(() => {
    if (Capacitor.getPlatform() === "ios") {
      setIsApple(true);
    }
  }, []);

  return (
    <div className="flex flex-col h-screen">
      <>
        <Header title={"search"} iOS={isApple} info={false} />

        <main className="flex-1 overflow-y-auto">
          <div className="pl-10 pr-10 pt-6">
            <label htmlFor="search" className="sr-only">
              Search
            </label>
            <div className="relative">
              <div className="pointer-events-none absolute inset-y-0 left-0 pl-3 flex items-center">
                <SearchIcon
                  className="h-5 w-5 text-gray-400"
                  aria-hidden="true"
                />
              </div>
              <input
                id="search"
                name="search"
                className="block w-full bg-white border border-gray-300 rounded-md py-2 pl-10 pr-3 placeholder-gray-500 focus:outline-none focus:text-gray-900 focus:placeholder-gray-400 focus:ring-1focus:border-gray-800"
                placeholder="Search"
                type="search"
                value={searchInput}
                onChange={(event) => setSearchInput(event.target.value)}
                autoCapitalize="off"
              />
            </div>
          </div>
          <div>
            <ul className="divide-y divide-gray-200">
              {tags

                .filter(
                  (tag) =>
                    tag.name.includes(searchInput) ||
                    (tag.synonyms && tag.synonyms.includes(searchInput))
                )

                .map((filteredTag, index) => {
                  return (
                    <li key={filteredTag.id}>
                      <Link href={"/tag/" + filteredTag.slug}>
                     
                          <div className="flex items-center px-4 py-8 sm:px-6">
                            <div className="min-w-0 flex-1 flex items-center">
                              <div className="flex-shrink-0"></div>
                              <div className="min-w-0 flex-1 px-4 md:grid md:grid-cols-2 md:gap-4">
                                <div>
                                  <p className="font-medium text-greyDark opacity-90 ">
                                    {filteredTag.name}
                                  </p>
                                </div>
                              </div>
                            </div>
                            <div>
                              <ChevronRightIcon
                                className="h-5 w-5 text-greyDark opacity-90"
                                aria-hidden="true"
                              />
                            </div>
                          </div>
                   
                      </Link>
                    </li>
                  );
                })}

              {orderedTags.map((filteredTag, index) => {
                const test =
                  filteredTag.synonyms &&
                  JSON.parse(filteredTag.synonyms).map((synonym, index) => {
                    return (
                      <li key={index}>
                        <Link href={"/tag/" + filteredTag.slug}>
                       
                            <div className="flex items-center px-4 py-8 sm:px-6">
                              <div className="min-w-0 flex-1 flex items-center">
                                <div className="flex-shrink-0"></div>
                                <div className="min-w-0 flex-1 px-4 md:grid md:grid-cols-2 md:gap-4">
                                  <div>
                                    <p className="font-medium text-greyDark opacity-90 ">
                                      {synonym}
                                    </p>
                                  </div>
                                </div>
                              </div>
                              <div>
                                <ChevronRightIcon
                                  className="h-5 w-5 text-greyDark opacity-90"
                                  aria-hidden="true"
                                />
                              </div>
                            </div>
                      
                        </Link>
                      </li>
                    );
                  });
                return test;
              })}
            </ul>
          </div>
        </main>
      </>
    </div>
  );
}

export async function getStaticProps() {
  // const res = await fetch(
  //   process.env.NEXT_PUBLIC_FIREBASE_API_URL + "/api/tag"
  // );

  // const tags = await res.json();

  const { data, error } = await supabase.from("tags").select("*");

  const tags = data;

  return {
    props: { tags },
  };
}
