import useUser from "@lib/useUser";
import { useRouter } from "next/router";
import Card from "@components/ui/Card";
import fetchJson from "@lib/fetchJson";
import { useState, useEffect } from "react";
import BottomNav from "@components/ui/BottomNav";
import Header from "@components/ui/Header";
import { Capacitor } from "@capacitor/core";
import { Oval } from "svg-loaders-react";
import WelcomeView from "@components/ui/Welcome";
import { supabase } from "@utils/supabaseClient";
import LoginForm from "@components/ui/LoginForm";

// supabase local session
// const sbLocalTemp =
//   typeof window !== "undefined" &&
//   localStorage.getItem("sb-jvyolfslzfwiyhppvkxd-auth-token");
// const user = JSON.parse(sbLocalTemp);

export default function Account() {
  const [userData, setUserData] = useState(null);
  const router = useRouter();
  const [isApple, setIsApple] = useState(false);
  const [showInfo, setShowInfo] = useState(false);
  const [userGroupExp, setUserGroupExp] = useState(false);
  const [showDelete, setShowDelete] = useState(false);
  const [session, setSession] = useState(null);
  const [loading, setLoading] = useState(false);
  const [whiteLabelUser, setWhiteLabelUser] = useState(null);
  const [displayWhiteLabel, setDisplayWhiteLabel] = useState(null);

  /* --- DEBUG --- */

  // console.log("session");
  // console.log(session);

  // console.log("userData");
  // console.log(userData);

  // console.log("whiteLabelUser");
  // console.log(whiteLabelUser);

  /* --- DEBUG --- */

  useEffect(() => {
    supabase.auth.getSession().then(({ data: { session } }) => {
      setSession(session);
    });

    supabase.auth.onAuthStateChange((_event, session) => {
      setSession(session);
    });
  }, []);

  useEffect(() => {
    let mounted = true;
    if (Capacitor.getPlatform() === "ios") {
      setIsApple(true);
    }
    getUserProfile();
    return function cleanup() {
      mounted = false;
    };
  }, [session]);

  const toggleInfo = () => {
    setShowInfo(!showInfo);
  };

  const toggleDelete = () => {
    setShowDelete((s) => !s);
  };

  const deleteUserAccount = async () => {
    // mutateUser(await fetchJson("/api/logout", { method: "POST" }), false);
    router.push("/delete-account");
  };

  const goBack = () => {
    window.history.back();
  };

  async function getUserProfile() {
    try {
      setLoading(true);

      const {
        data: { user },
      } = await supabase.auth.getUser();

      let { data, error, status } = await supabase
        .from("profiles")
        .select("*")
        .eq("id", user.id)
        .single();

      if (error && status !== 406) {
        throw error;
      }

      if (data) {
        setUserData(data);

        // check date

        const userExpDate = data.white_label;
        // console.log("userExpDate");
        // console.log(userExpDate);

        if (userExpDate !== null) {
          const date = new Date();

          let day = date.getDate();
          let month = date.getMonth() + 1;
          let year = date.getFullYear();

          let currentDateTmp = `${year}-${month}-${day}`;
          const currentDate = currentDateTmp;

          // console.log("currentDate");
          // console.log(currentDate);

          const userExpDateParse = Date.parse(userExpDate);
          const currDateParse = Date.parse(currentDate);

          // if the user has white label access set the flag here (valid date)
          if (userExpDateParse < currDateParse) {
            setWhiteLabelUser(false);
            setDisplayWhiteLabel(true);
          } else {
            setWhiteLabelUser(true);
            setDisplayWhiteLabel(true);
          }
        } else {
          setDisplayWhiteLabel(false);
        }
      }
    } catch (error) {
      // console.log("user not logged in");
    } finally {
      setLoading(false);
    }
  }

  return (
    <>
      {loading ? (
        <div className="grid h-screen place-items-center">
          <div className="mt-10 mb-10 flex flex-row space-x-2">
            <Oval stroke="#0c39ac" />
          </div>
        </div>
      ) : (
        <>
          {!session ? (
            <>
              <div className="flex flex-col h-screen">
                <Header iOS={isApple} toggleInfo={toggleInfo} info={true} />
                <main className="flex-1 overflow-y-auto">
                  <div>
                    <div>
                      <LoginForm />
                    </div>
                  </div>
                </main>

                <BottomNav name="account" />
              </div>
            </>
          ) : (
            <div className="flex flex-col h-screen">
              {showInfo ? (
                <WelcomeView
                  toggleInfo={toggleInfo}
                  // title={category.name}
                  iOS={isApple}
                />
              ) : (
                <>
                  <Header iOS={isApple} toggleInfo={toggleInfo} info={true} />
                  <main className="flex-1 overflow-y-auto">
                    <div className="sm:mx-auto sm:w-full sm:max-w-md">
                      <h2 className="font-PTSans mt-6 text-center text-2xl font-extrabold text-gray-800">
                        Account details
                      </h2>
                    </div>
                    {showDelete ? (
                      <Card className="my-3 mx-auto">
                        <div className="pt-6 px-6 py-5 bg-white">
                          <div className="pb-3">
                            <h2 className="text-xl">
                              Confirm account deletion
                            </h2>
                            <p className="pt-3 text-base">
                              Your account for the email address{" "}
                              {userData.email} will be deleted and you will no
                              longer have access to your bookshelf
                            </p>
                          </div>
                        </div>

                        <div className="p-6 pt-0">
                          <button
                            onClick={() => toggleDelete()}
                            className=" bg-blue-800 w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-secondary focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                          >
                            Go back
                          </button>
                        </div>

                        <div className="p-6 pt-0">
                          <button
                            onClick={() => deleteUserAccount()}
                            className=" bg-red-600 w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-secondary focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                          >
                            Confirm delete account
                          </button>
                        </div>
                      </Card>
                    ) : (
                      <Card className="my-3 mx-auto">
                        <div className="pt-6 px-6 py-5 bg-white">
                          <div className="pb-3">
                            <p></p>
                            <div className="mt-1">
                              <label className="p-3 text-primary font-semibold">
                                Name:{" "}
                              </label>
                              <p className="pl-3">
                                {userData && userData.first_name}{" "}
                                {userData && userData.last_name}
                              </p>
                            </div>
                          </div>
                          <div className="pb-3">
                            <div className="mt-1">
                              <label className="p-3 text-primary font-semibold">
                                Email:{" "}
                              </label>
                              <input
                                disabled
                                value={userData && userData.email}
                                className="appearance-none block w-full px-3 py-2 rounded-md focus:outline-none"
                              />
                            </div>
                          </div>
                          {displayWhiteLabel && (
                            <div className="pb-0">
                              <div className="mt-1">
                                {whiteLabelUser ? (
                                  <label className="p-3 text-primary font-semibold">
                                    ebookshelf access expires:{" "}
                                  </label>
                                ) : (
                                  <label className="p-3 text-error">
                                    ebookshelf access expired:{" "}
                                  </label>
                                )}
                                <input
                                  disabled
                                  value={userData && userData.white_label}
                                  className="appearance-none block w-full px-3 py-2 rounded-md focus:outline-none"
                                />
                              </div>
                            </div>
                          )}
                        </div>
                        <div className="p-6">
                          <button
                            onClick={() => supabase.auth.signOut()}
                            className=" bg-blue-800 w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-secondary focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                          >
                            Sign out
                          </button>
                        </div>
                        <div className="p-6 pt-0">
                          <button
                            onClick={() => toggleDelete()}
                            className=" bg-red-600 w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-secondary focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                          >
                            Delete account
                          </button>
                        </div>
                      </Card>
                    )}
                  </main>
                </>
              )}
              <BottomNav name="account" showEbooks={userGroupExp} />
            </div>
          )}
        </>
      )}
    </>
  );
}
