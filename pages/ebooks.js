import { useState, useEffect } from "react";
import useUser from "@lib/useUser";
import { useRouter } from "next/router";
import BottomNav from "../components/ui/BottomNav";
import Header from "../components/ui/Header";
import { Capacitor } from "@capacitor/core";
import Image from "next/image";
import Link from "next/link";
import { ChevronRightIcon } from "@heroicons/react/solid";
import { Oval } from "svg-loaders-react";
import WelcomeView from "@components/ui/Welcome";
import { SearchIcon } from "@heroicons/react/solid";
import { supabase } from "@utils/supabaseClient";
import LoginForm from "@components/ui/LoginForm";

export default function eBooks({ books }) {
  const [session, setSession] = useState(null);
  const [userProfile, setUserProfile] = useState(null);
  const [userData, setUserData] = useState(null);
  const [loading, setLoading] = useState(true);
  const router = useRouter();
  const [isApple, setIsApple] = useState(false);
  const [showInfo, setShowInfo] = useState(false);
  const [userGroups, setUserGroups] = useState(false);
  const [userGroupExp, setUserGroupExp] = useState(false);
  const [whiteLabelUser, setWhiteLabelUser] = useState(null);
  const [searchInput, setSearchInput] = useState("");

  // ** DEBUG ** //

  // console.log("session");
  // console.log(session);

  // console.log("userProfile");
  // console.log(userProfile);

  // console.log("whiteLabelUser");
  // console.log(whiteLabelUser);

  // console.log("books");
  // console.log(books);
  // console.log(user && user.groups && user.groups[0].expiry_date);
  // ** DEBUG ** //

  useEffect(() => {
    supabase.auth.getSession().then(({ data: { session } }) => {
      setSession(session);
    });

    supabase.auth.onAuthStateChange((_event, session) => {
      setSession(session);
    });
  }, []);

  useEffect(() => {
    let mounted = true;
    if (Capacitor.getPlatform() === "ios") {
      setIsApple(true);
    }
    getUserProfile();
    return function cleanup() {
      mounted = false;
    };
  }, [session]);

  const toggleInfo = () => {
    setShowInfo(!showInfo);
  };

  async function getUserProfile() {
    try {
      setLoading(true);

      const {
        data: { user },
      } = await supabase.auth.getUser();

      let { data, error, status } = await supabase
        .from("profiles")
        .select("*")
        .eq("id", user.id)
        .single();

      if (error && status !== 406) {
        throw error;
      }

      if (data) {
        setUserProfile(data);
        // setWhiteLabelUser(data.active);

        // check date

        const userExpDate = data.white_label;

        if (userExpDate !== null) {
          const date = new Date();

          let day = date.getDate();
          let month = date.getMonth() + 1;
          let year = date.getFullYear();

          let currentDateTmp = `${year}-${month}-${day}`;
          const currentDate = currentDateTmp;

          const userExpDateParse = Date.parse(userExpDate);
          const currDateParse = Date.parse(currentDate);

          // if the user has white label access set the flag here (valid date)
          if (userExpDateParse < currDateParse) {
            setWhiteLabelUser(false);
          } else {
            setWhiteLabelUser(true);
          }
        } else {
          setDisplayWhiteLabel(false);
        }
      }
    } catch (error) {
      // console.log("user not logged in");
    } finally {
      setLoading(false);
    }
  }

  // Add user started_reading_ebook

  async function addUserEvent(name) {
    try {
      setLoading(true);

      const dataEnter = {
        user_id: userProfile.id,
        event_type: "started_reading_book_" + name,
        organisation: userProfile.organisation,
        profession: userProfile.profession,
        country: userProfile.country,
      };

      const { data, error } = await supabase
        .from("events")
        .insert(dataEnter)
        .select();

      if (error) {
        console.log(error);
        throw error;
      }
    } catch (error) {
      console.log(error.message);
    } finally {
      // trigger created_account event
      setLoading(false);
    }
  }

  const filteredEbooks = books.sort(function (a, b) {
    var textA = a.name.toUpperCase();
    var textB = b.name.toUpperCase();
    return textA < textB ? -1 : textA > textB ? 1 : 0;
  });

  // console.log(filteredEbooks);

  const usersBooks = filteredEbooks

    .filter((book) => book.name && book.name.includes(searchInput))

    .map((book, index) => {
      return (
        <li key={book.slug} onClick={() => addUserEvent(book.name)}>
          <Link href={"/book/" + book.slug}>
            <div className="flex items-center px-4 py-4 sm:px-6">
              <div className="min-w-0 flex-1 flex items-center">
                {/* <div>{book[0].id}</div> */}
                <div className="flex-shrink-0">
                  {book.thumbnail && (
                    <img
                      className="w-24 rounded-md"
                      src={`data:image/jpeg;base64,${book.thumbnail}`}
                    />
                  )}
                </div>
                <div className="min-w-0 flex-1 px-4 md:grid md:grid-cols-2 md:gap-4">
                  <div>
                    <p className="font-medium text-greyDark opacity-90 ">
                      {book.name}
                    </p>
                  </div>
                </div>
              </div>
              <div>
                <ChevronRightIcon
                  className="h-5 w-5 text-greyDark opacity-90"
                  aria-hidden="true"
                />
              </div>
            </div>
          </Link>
        </li>
      );
    });

  return (
    <>
      {loading ? (
        <div className="grid h-screen place-items-center">
          <div className="mt-10 mb-10 flex flex-row space-x-2">
            <Oval stroke="#0c39ac" />
          </div>
        </div>
      ) : (
        <>
          {!session ? (
            <>
              <div className="flex flex-col h-screen">
                <Header iOS={isApple} toggleInfo={toggleInfo} info={true} />
                <main className="flex-1 overflow-y-auto">
                  <div>
                    <div>
                      <LoginForm />
                    </div>
                  </div>
                </main>

                <BottomNav name="e-books" />
              </div>
            </>
          ) : (
            <div className="flex flex-col h-screen">
              {showInfo ? (
                <WelcomeView
                  toggleInfo={toggleInfo}
                  // title={category.name}
                  iOS={isApple}
                />
              ) : (
                <>
                  <Header iOS={isApple} toggleInfo={toggleInfo} info={true} />
                  {whiteLabelUser ? (
                    <div className="pl-10 pr-10 pt-6">
                      <label htmlFor="search" className="sr-only">
                        Search
                      </label>
                      <div className="relative">
                        <div className="pointer-events-none absolute inset-y-0 left-0 pl-3 flex items-center">
                          <SearchIcon
                            className="h-5 w-5 text-gray-400"
                            aria-hidden="true"
                          />
                        </div>
                        <input
                          id="search"
                          name="search"
                          className="block w-full bg-white border border-gray-300 rounded-md py-2 pl-10 pr-3  placeholder-gray-500 focus:outline-none focus:text-gray-900 focus:placeholder-gray-400 focus:ring-1focus:border-gray-800"
                          placeholder="Search"
                          type="search"
                          value={searchInput}
                          onChange={(event) =>
                            setSearchInput(event.target.value)
                          }
                          autoCapitalize="off"
                        />
                      </div>
                    </div>
                  ) : null}
                  <main className="flex-1 overflow-y-auto">
                    {!whiteLabelUser ? (
                      <div className="p-6 font-PTSans">
                        <p className="text-xl text-primary p-3">
                          You need a valid licence to view the e-bookshelf
                        </p>

                        <p className="p-2">
                          If you would like have access to the e-bookshelf and
                          all the short stories in the app, please contact us
                          for information on how to obtain a licence.
                        </p>
                        <p className="p-2 text-primary">
                          <a href="mailto: <EMAIL>">
                            <EMAIL>
                          </a>
                        </p>
                      </div>
                    ) : (
                      <div>
                        <ul className="divide-y divide-gray-200">
                          {usersBooks}
                        </ul>
                      </div>
                    )}
                  </main>
                </>
              )}
              <BottomNav name="ebooks" />
            </div>
          )}
        </>
      )}
    </>
  );
}

export async function getStaticProps() {
  // const res = await fetch(
  //   process.env.NEXT_PUBLIC_FIREBASE_API_URL + "/api/book"
  // );

  // const books = await res.json();

  const { data, error } = await supabase
    .from("books")
    .select(`name, slug, legacy_id, bookshop_link, info, thumbnail`);
  // console.log(data);

  const books = data;

  return {
    props: { books },
  };
}
