import { useState, useEffect } from "react";
import TopNav from "@components/ui/TopNav";
import InfoView from "@components/ui/Info";
import SlideShow from "@components/ui/SlideShow";
import { Capacitor } from "@capacitor/core";
import Link from "next/link";
import { ChevronRightIcon } from "@heroicons/react/solid";
import { supabase } from "@utils/supabaseClient";

export default function Tag({ stories, slug }) {
  const [isApple, setIsApple] = useState(false);
  // console.log(stories);

  // chanmge the order a-z
  const filteredStories = stories.sort(function (a, b) {
    var textA = a.name.toUpperCase();
    var textB = b.name.toUpperCase();
    return textA < textB ? -1 : textA > textB ? 1 : 0;
  });

  const [index, setIndex] = useState(0);
  const [showInfo, setShowInfo] = useState(false);
  const [showSlideShow, setSlideShow] = useState(true);

  const toggleInfo = () => {
    setShowInfo(!showInfo);
  };

  const toggleSlideshow = (index) => {
    setIndex(index);
    setSlideShow(!showSlideShow);
  };

  useEffect(() => {
    if (Capacitor.getPlatform() === "ios") {
      setIsApple(true);
    }
  }, []);

  return (
    <>
      <TopNav title={slug} iOS={isApple} />
      <main className="flex-1 overflow-y-auto">
        <div className="pt-3 pl-3 pr-3">
          <ul className="divide-y divide-gray-200">
            {filteredStories.map((story, index) => (
              <li key={index}>
                <Link href={"/story/" + story.slug}>
             
                    <div className="flex items-center px-4 py-4 sm:px-6">
                      <div className="min-w-0 flex-1 flex items-center">
                        <div className="flex-shrink-0">
                          {story.thumbnail && (
                            <img
                              className="w-24 rounded-md"
                              src={`data:image/jpeg;base64,${story.thumbnail}`}
                            />
                          )}
                        </div>
                        <div className="min-w-0 flex-1 px-4 md:grid md:grid-cols-2 md:gap-4">
                          <div>
                            <p className="font-medium text-greyDark opacity-90 ">
                              {story.name}
                            </p>
                          </div>
                        </div>
                      </div>
                      <div>
                        <ChevronRightIcon
                          className="h-5 w-5 text-greyDark opacity-90"
                          aria-hidden="true"
                        />
                      </div>
                    </div>
                
                </Link>
              </li>
            ))}
          </ul>
        </div>
      </main>
    </>
  );
}

export async function getStaticPaths() {
  // const res = await fetch(
  //   process.env.NEXT_PUBLIC_FIREBASE_API_URL + "/api/tag"
  // );

  // const tags = await res.json();
  // console.log(tags);

  const { data, error } = await supabase.from("tags").select("*");

  const tags = data;

  const paths = tags.map((tag) => ({
    params: { slug: tag.slug },
  }));

  return {
    paths,
    fallback: false,
  };
}

export async function getStaticProps({ params }) {
  const { slug } = params;

  // const res = await fetch(
  //   process.env.NEXT_PUBLIC_FIREBASE_API_URL + `/api/story/tag/${slug}`
  // );

  // const data = await res.json();

  // const stories = data;

  // first we need to get the ID

  let tagId;
  let stories;
  // let stories;

  // fetch the stories that contain that tag ID (-MZhph6YdY_Yy9VpaFm-)

  try {
    const { data, error } = await supabase
      .from("tags")
      .select("*")
      .eq("slug", slug)
      .single();

    if (data) {
      tagId = data.legacy_id;
      // console.log(data);
    }
  } catch (error) {
    console.log(error);
  } finally {
    try {
      const { data, error } = await supabase
        .from("stories")
        .select("*")
        .textSearch("tags", [tagId]);
      stories = data;
      // console.log(data);
      if (data) {
      }
    } catch (error) {
      console.log(error);
    } finally {
    }
  }

  return {
    props: { stories, slug },
  };
}
