import React, { useState, useEffect } from "react";
import Header from "@components/ui/Header";
import BottomNav from "@components/ui/BottomNav";
import { Capacitor } from "@capacitor/core";
import Link from "next/link";
import { Oval } from "svg-loaders-react";
import { supabase } from "@utils/supabaseClient";
import { EyeIcon } from "@heroicons/react/solid";
import { EyeOffIcon } from "@heroicons/react/solid";

// import RegisterForm from "@components/ui/RegisterForm";

const ResetPassword = () => {
  const [isApple, setIsApple] = useState(false);

  const [loading, setLoading] = useState(false);

  const [password, setPassword] = useState({
    entry: "",
    type: "password",
  });

  const [passwordChanged, setPasswordChanged] = useState(false);
  const [errorOnSubmit, setErrorOnSubmit] = useState(false);

  const [type, setType] = useState("password");
  const [icon, setIcon] = useState(EyeOffIcon);

  const [toggle, setToggle] = useState(false);

  /* --- DEBUG --- */

  // console.log("errorOnSubmit");
  // console.log(errorOnSubmit);

  // console.log("password");
  // console.log(password);

  console.log(toggle);

  /* --- DEBUG --- */

  useEffect(() => {
    let mounted = true;

    if (Capacitor.getPlatform() === "ios") {
      setIsApple(true);
    }

    return function cleanup() {
      mounted = false;
    };
  }, []);

  const togglePassword = () => {
    setToggle((prev) => !prev);

    if (type === "password") {
      // setIcon(EyeIcon);
      setType("text");
    } else {
      // setIcon(EyeOffIcon);
      setType("password");
    }
  };

  // validate form

  const validateForm = () => {
    let str = password.entry;

    // check password isn't empty

    if (str.trim().length === 0) {
      setErrorOnSubmit("Please enter a password");
      // display error
    } else {
      setErrorOnSubmit("");
      validatePass();
    }
    // check password is at least 6

    function validatePass() {
      if (password.entry.length >= 6) {
        resetPassword();
        setErrorOnSubmit("");
      } else {
        setErrorOnSubmit("Your password needs to be at least 6 characters");
      }
    }
  };

  // reset pass
  async function resetPassword() {
    const userDataToSend = {
      password: password.entry,
    };

    try {
      setLoading(true);

      const { data, error } = await supabase.auth.updateUser({
        password: userDataToSend.password,
      });

      if (error) {
        throw error;
      } else {
        setErrorOnSubmit(false);
        setPasswordChanged(true);
      }
    } catch (error) {
      console.log(error.error_description || error.message);
      // setErrorOnSubmit(error.message);
      setErrorOnSubmit(
        "Sorry the email link has expired OR you have entered your previous password. Please try a different password or return to the login to request a reset again."
      );
    } finally {
      setLoading(false);
    }
  }

  // update currSelection when entry changes
  const updateSelection = (e) => {
    const { value: value } = e.target;
    const type = e.target.id;

    // if (e.target.value !== "") {
    switch (type) {
      case "password":
        setPassword({
          entry: value,
          type: type,
        });

        return;

      default:
        return;
    }
    // }
  };

  return (
    <>
      {loading ? (
        <div className="grid h-screen place-items-center">
          <div className="mt-10 mb-10 flex flex-row space-x-2">
            <Oval stroke="#0c39ac" />
          </div>
        </div>
      ) : (
        <>
          <div className="flex flex-col h-screen">
            <Header iOS={isApple} info={false} />

            <main className="flex-1 overflow-y-auto bg-gray-100 ">
              {passwordChanged ? (
                <div className="flex  flex-col justify-center py-12 sm:px-6 lg:px-8 p-3">
                  <div>
                    <h2 className="font-Montserrat mt-6 text-center text-2xl font-extrabold text-gray-800">
                      Password reset
                    </h2>
                    {/* <p className="font-Montserrat mt-6 text-center text-gray-800">
                      If an account for that email exists then you will recieve
                      an email with a link to reset your password.
                    </p> */}
                  </div>
                  <div className="mt-6 p-6">
                    <Link href="/">
                      <button className=" bg-blue-800 w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-secondary focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                        Return to login
                      </button>
                    </Link>
                  </div>
                </div>
              ) : (
                // <RegisterForm errorMessage={errorMsg} />
                <div className="bg-gray-100 flex flex-col justify-center py-12 sm:px-6 lg:px-8">
                  <div className="sm:mx-auto sm:w-full sm:max-w-md">
                    <div>
                      <h2 className="font-Montserrat mt-6 text-center text-2xl font-extrabold text-gray-800 p-3">
                        Set a new password
                      </h2>
                    </div>
                  </div>

                  <div className="mt-3 sm:mx-auto sm:w-full sm:max-w-md m-6">
                    <div className="bg-white py-8 px-4 shadow sm:rounded-lg sm:px-10">
                      <div className="pb-3">
                        <label
                          htmlFor="password"
                          className="block text-sm font-medium text-gray-700"
                        >
                          Password
                        </label>
                        {/* <div className="mt-1">
                          <input
                            value={(password && password.entry) || ""}
                            onChange={updateSelection}
                            type={type}
                            name="password"
                            id="password"
                            className="appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                          />
                        </div> */}

                        <div className="relative w-full">
                          <div className="absolute inset-y-0 right-0 flex items-center px-2">
                            <label
                              className="bg-gray-300 hover:bg-gray-300 rounded px-2 py-1 text-sm text-gray-600 font-mono cursor-pointer js-password-label"
                              onClick={() => togglePassword()}
                            >
                              {toggle ? "hide" : "show"}
                            </label>
                          </div>
                          <input
                            className="appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                            value={(password && password.entry) || ""}
                            onChange={updateSelection}
                            type={type}
                            name="password"
                            id="password"
                          />
                        </div>
                        {/* <div className="relative w-full">
                          <div className="absolute inset-y-0 right-0 flex items-center px-2">
                            <label
                              className="bg-gray-300 hover:bg-gray-400 rounded px-2 py-1 text-sm text-gray-600 font-mono cursor-pointer js-password-label"
                              onClick={() => togglePassword()}
                            >
                              {toggle ? "hide" : "show"}
                            </label>
                          </div>
                          <input
                            className="appearance-none border-2 rounded w-full py-3 px-3 leading-tight border-gray-300 bg-gray-100 focus:outline-none focus:border-indigo-700 focus:bg-white text-gray-700 pr-16 font-mono js-password"
                            id="password"
                            // type="password"
                            type={type}
                          />
                        </div> */}
                      </div>

                      <div className="mt-6">
                        <button
                          onClick={validateForm}
                          className=" bg-green-500 w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-secondary focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                        >
                          Save new password
                        </button>

                        {errorOnSubmit ? (
                          <div className="text-error pt-2">{errorOnSubmit}</div>
                        ) : null}
                      </div>
                    </div>
                  </div>
                </div>
              )}
            </main>
            <BottomNav name="account" />
          </div>
        </>
      )}
    </>
  );
};

export default ResetPassword;
