import { useState, useEffect } from "react";
import TopNav from "@components/ui/TopNav";
import Link from "next/link";
import { ChevronRightIcon } from "@heroicons/react/solid";
import InfoView from "@components/ui/Info";
import { Capacitor } from "@capacitor/core";
import { supabase } from "@utils/supabaseClient";

export default function Category({ category, subcategories }) {
  console.log(category);

  const subcategoriesCurr = category.subcategories;
  // console.log("subcategoriesCurr");
  // console.log(subcategoriesCurr);

  // console.log("subcategories");
  // console.log(subcategories);

  // filter the required sub category data
  let subArray = subcategories.filter(function (item) {
    return subcategoriesCurr.indexOf(item.legacy_id) !== -1;
  });

  // console.log(subArray);

  // chanmge the order
  const filteredSubcategories = subArray.sort(function (a, b) {
    var textA = a.name.toUpperCase();
    var textB = b.name.toUpperCase();
    return textA < textB ? -1 : textA > textB ? 1 : 0;
  });

  const [showInfo, setShowInfo] = useState(false);
  const [isApple, setIsApple] = useState(false);

  const toggleInfo = () => {
    setShowInfo(!showInfo);
  };

  useEffect(() => {
    if (Capacitor.getPlatform() === "ios") {
      setIsApple(true);
    }
  }, []);

  return (
    <div className="flex flex-col h-screen">
      {showInfo ? (
        <InfoView
          info={category.info}
          toggleInfo={toggleInfo}
          title={category.name}
          iOS={isApple}
        />
      ) : (
        <>
          <TopNav title={category.name} toggleInfo={toggleInfo} iOS={isApple} />
          <main className="flex-1 overflow-y-auto">
            <div>
              <ul className="divide-y divide-gray-200">
                {filteredSubcategories &&
                  filteredSubcategories.map((cat, index) => (
                    <li key={cat.slug}>
                      <Link key={index} href={"/subcategory/" + cat.slug}>
                        <div className="flex items-center px-4 py-8 sm:px-6">
                          <div className="min-w-0 flex-1 flex items-center">
                            <div className="flex-shrink-0"></div>
                            <div className="min-w-0 flex-1 px-4 md:grid md:grid-cols-2 md:gap-4">
                              <div>
                                <p className="font-medium text-greyDark opacity-90 ">
                                  {cat.name}
                                </p>
                              </div>
                            </div>
                          </div>
                          <div>
                            <ChevronRightIcon
                              className="h-5 w-5 text-greyDark opacity-90"
                              aria-hidden="true"
                            />
                          </div>
                        </div>
                      </Link>
                    </li>
                  ))}
              </ul>
            </div>
          </main>
        </>
      )}
    </div>
  );
}

export async function getStaticPaths() {
  const { data, error } = await supabase.from("categories").select("*");

  const categories = data;

  const paths = categories.map((cat) => ({
    params: { slug: cat.slug },
  }));

  return {
    paths,
    fallback: false,
  };
}

export async function getStaticProps({ params }) {
  const { slug } = params;

  let category;
  let subcategories;
  let subs = [];

  try {
    const { data, error } = await supabase
      .from("categories")

      .select(`name, slug, legacy_id, bookshop_link, subcategories, info`)
      .eq("slug", slug)
      .single();

    category = data;

    // subs = data.subcategories;

    // console.log("subs");
    // console.log(subs);

    // here we can get the sub categories array

    if (data) {
    }
  } catch (error) {
    // console.log("user not logged in");
  } finally {
    // console.log("subs");
    // console.log(subs);

    // const subst = ["-Mf7qJM-pzA3q-xXmeZy", "-Mf7qJWkaPVv4jzF8N4X"];

    const { data, error } = await supabase.from("subcategories").select("*");
    // .in("legacy_id", subst);

    subcategories = data;

    // console.log("data");
    // console.log(data);
  }

  return {
    props: { category, subcategories },
  };
}
