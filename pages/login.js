import React, { useState, useEffect } from "react";
import { useRouter } from "next/router";
import fetchJson from "@lib/fetchJson";
import Header from "@components/ui/Header";
import BottomNav from "@components/ui/BottomNav";
import { Capacitor } from "@capacitor/core";

import Form from "@components/ui/LoginForm";
import ResetForm from "@components/ui/PassResetForm";

const Login = () => {
  const [resetPass, setResetPass] = useState(false);
  const [email, setEmail] = useState();
  const [showInfo, setShowInfo] = useState(false);
  const [showActivateScreen, setShowActivateScreen] = useState(false);

  const toggleInfo = () => {
    setShowInfo(!showInfo);
  };

  const [errorMsg, setErrorMsg] = useState("");
  const [isApple, setIsApple] = useState(false);

  useEffect(() => {
    if (Capacitor.getPlatform() === "ios") {
      setIsApple(true);
    }
  }, []);

  // async function handleSubmit(e) {
  //   e.preventDefault();

  //   const body = {
  //     email: e.target.email.value,
  //     password: e.target.password.value,
  //   };
  // }

  // async function handlePassChange(e) {
  //   e.preventDefault();

  //   const body = {
  //     email: email,
  //     password: e.target.password.value,
  //   };

  //   try {
  //     await fetchJson("/api/reset", {
  //       method: "POST",
  //       headers: { "Content-Type": "application/json" },
  //       body: JSON.stringify(body),
  //     }).then(setResetPass(false));
  //   } catch (error) {
  //     console.error("An unexpected error happened:", error);
  //     setErrorMsg(error.message);
  //   }
  // }

  return (
    <div className="flex flex-col h-screen">
      <Header iOS={isApple} toggleInfo={toggleInfo} info={true} />

      {showActivateScreen ? (
        <main className="flex-1 overflow-y-auto bg-gray-100 ">
          <div className="flex  flex-col justify-center py-12 sm:px-6 lg:px-8">
            <div className="sm:mx-auto sm:w-full sm:max-w-md">
              {/* <div className="mx-auto flex justify-center p-6">
          <img
            src="/img/logo.png"
            className="object-contain pb-1 h-12 w-full"
          />
        </div> */}

              <div>
                <h2 className="font-Montserrat text-center text-2xl font-extrabold text-gray-800">
                  Sign into your account
                </h2>
              </div>
            </div>

            <div className="mt-8 sm:mx-auto sm:w-full sm:max-w-md m-6">
              <div className="bg-white py-8 px-4 shadow sm:rounded-lg sm:px-10">
                <div>
                  <p className="mb-3 font-PTSans">
                    As this is your first login to the new app we you need to
                    reset your password.
                  </p>

                  <p className="mb-3 font-PTSans">
                    We have sent you an email with a link that will let you do
                    this
                  </p>
                </div>
              </div>
            </div>
          </div>
        </main>
      ) : (
        <main className="flex-1 overflow-y-auto bg-gray-100 ">
          {resetPass ? (
            <ResetForm email={email} />
          ) : (
            <Form isLogin errorMessage={errorMsg} />
          )}
        </main>
      )}

      <BottomNav name="account" />
    </div>
  );
};

export default Login;
