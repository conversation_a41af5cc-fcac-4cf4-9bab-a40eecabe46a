import { useState, useEffect } from "react";
import TopNav from "@components/ui/TopNav";
import InfoView from "@components/ui/Info";
import SlideShow from "@components/ui/SlideShow";
import { Capacitor } from "@capacitor/core";
import { supabase } from "@utils/supabaseClient";

export default function Story({ book, bookImages }) {
  const [userData, setUserData] = useState(null);
  const [isApple, setIsApple] = useState(false);
  const [session, setSession] = useState(null);
  const [loading, setLoading] = useState(false);

  // console.log(book);

  const imagesCurr = book && book.images;

  // console.log(imagesCurr)

  const imagesCurrObj = JSON.parse(imagesCurr);

  // console.log("imagesCurrObj")
  // console.log(imagesCurrObj)

  // console.log("bookImages");
  // console.log(bookImages);

  // const imagesArray = bookImages;

  // filter the required images data

  // const imagesCurrObj = JSON.parse(imagesCurr);

  // const imagesArray = bookImages.filter(function (item) {
  //   return imagesCurr.indexOf(item.legacy_id) !== -1;
  // });

  // const imagesArrayCurr = imagesCurrObj.filter(function (item) {
  //   return JSON.stringify(imagesArray).indexOf(item.ID) !== -1;
  // });

  useEffect(() => {
    let mounted = true;
    // if (Capacitor.getPlatform() === "ios") {
    //   setIsApple(true);
    // }
    getUserProfile();
    return function cleanup() {
      mounted = false;
    };
  }, [session]);

  async function getUserProfile() {
    try {
      setLoading(true);

      const {
        data: { user },
      } = await supabase.auth.getUser();

      let { data, error, status } = await supabase
        .from("profiles")
        .select("*")
        .eq("id", user.id)
        .single();

      if (error && status !== 406) {
        throw error;
      }

      if (data) {
        setUserData(data);

        // console.log(data);
      }
    } catch (error) {
      // console.log("user not logged in");
    } finally {
      setLoading(false);
    }
  }

  const mergeData = (arr1 = [], arr2 = []) => {
    let res = [];
    res = arr1.map((obj) => {
      const index = arr2.findIndex((el) => el["ID"] == obj["legacy_id"]);
      const { address, Order } = index !== -1 ? arr2[index] : {};
      return {
        ...obj,
        Order,
      };
    });
    return res;
  };

  // merge to get the Order field
  const imagesWithOrder = mergeData(bookImages, imagesCurrObj);

  // Sort by order field
  const images = imagesWithOrder.sort(function (a, b) {
    var orderA = parseInt(a.Order);
    var orderB = parseInt(b.Order);
    return orderA < orderB ? -1 : orderA > orderB ? 1 : 0;
  });

  // console.log("images");
  // console.log(images);

  const thumbnail = book && book.thumbnail;
  const shopLink = book && book.bookshop_link;

  const [index, setIndex] = useState(0);
  const [showInfo, setShowInfo] = useState(false);
  const [showSlideShow, setSlideShow] = useState(true);

  const toggleInfo = () => {
    setShowInfo(!showInfo);
  };

  const toggleSlideshow = (index) => {
    setIndex(index);
    setSlideShow(!showSlideShow);
  };

  useEffect(() => {
    if (Capacitor.getPlatform() === "ios") {
      setIsApple(true);
    }
  }, []);

  return (
    <>
      {showSlideShow ? (
        <SlideShow
          slides={images && images}
          title={book && book.name}
          IndexNum={index}
          toggleSlideshow={toggleSlideshow}
          iOS={isApple}
          shopLink={shopLink && shopLink}
          thumbnail={thumbnail && thumbnail}
          userData={userData}
          isEbook={true}
        />
      ) : (
        <div className="flex flex-col h-screen">
          {showInfo ? (
            <InfoView
              blurb={book && book.info}
              toggleInfo={toggleInfo}
              title={book && book.name}
              iOS={isApple}
            />
          ) : (
            <>
              <TopNav
                title={book && book.name}
                toggleInfo={toggleInfo}
                iOS={isApple}
              />
              <main className="flex-1 overflow-y-auto">
                <div className="pt-3 pl-3 pr-3">
                  <ul role="list" className="grid grid-cols-3 gap-x-3 gap-y-3">
                    {images &&
                      images.map((img, index) => (
                        <li key={index} className="relative">
                          <div
                            className="group block w-full aspect-w-10 rounded-md border shadow-lg overflow-hidden"
                            onClick={() => {
                              toggleSlideshow(index);
                            }}
                          >
                            <img
                              src={`data:image/jpeg;base64,${img.image_string}`}
                              alt=""
                              className="object-cover pointer-events-none group-hover:opacity-75"
                            />
                          </div>
                        </li>
                      ))}
                  </ul>
                </div>
              </main>
            </>
          )}
        </div>
      )}
    </>
  );
}

export async function getStaticPaths() {
  // const res = await fetch(
  //   process.env.NEXT_PUBLIC_FIREBASE_API_URL + "/api/book"
  // );

  // const books = await res.json();

  // const paths = books.map((book) => ({
  //   params: { slug: book.slug },
  // }));

  const { data, error } = await supabase.from("books").select("*");

  const books = data;

  const paths =
    books &&
    books.map((cat) => ({
      params: { slug: cat.slug },
    }));

  return {
    paths,
    fallback: false,
  };
}

export async function getStaticProps({ params }) {
  const { slug } = params;

  let book;
  let bookImages;
  let imageIds;
  let imageIdsArray = [];

  try {
    const { data, error } = await supabase
      .from("books")
      .select("*")
      .eq("slug", slug)
      .single();

    book = data;

    if (data) {
      imageIds = JSON.parse(data.images);
      // console.log(imageIds);

      imageIds &&
        imageIds.map((img) => {
          // console.log(img.ID)
          imageIdsArray.push(img.ID);
        });

      // map through the array and extract just the ids to a new array
    }
  } catch (error) {
    console.log(error);
  } finally {
    try {
      const { data, error } = await supabase
        .from("images")
        .select("*")
        .in("legacy_id", [imageIdsArray]);
      // .contains('legacy_id', ["-Mf26090A7GPNeiHGEt1"]);
      //  .overlaps('legacy_id', ["-Mf26090A7GPNeiHGEt1"]);
      // .textSearch("legacy_id", [imageIds.ID]); // just need the write query here!
      bookImages = data;

      if (data) {
        // console.log(data);
        // console.log(imageIdsArray)
      }
    } catch (error) {
      console.log(error);
    } finally {
    }
  }

  // OLD VERSION

  // try {
  //   const { data, error } = await supabase
  //     .from("books")
  //     .select("*")
  //     .eq("slug", slug)
  //     .single();

  //   book = data;

  //   if (data) {
  //   }
  // } catch (error) {
  //   console.log(error);
  // } finally {
  //   //  get the images
  //   const { data, error } = await supabase.from("images").select("*");
  //   bookImages = data;
  // }

  return {
    props: { book, bookImages },
  };
}
