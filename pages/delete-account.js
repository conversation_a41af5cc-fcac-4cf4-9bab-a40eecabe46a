import React, { useState, useEffect } from "react";
// import useUser from "@lib/useUser";
import { useRouter } from "next/router";
import fetchJson from "@lib/fetchJson";
import Header from "@components/ui/Header";
import BottomNav from "@components/ui/BottomNav";
import { Capacitor } from "@capacitor/core";
import DeleteForm from "@components/ui/DeleteForm";
import { loginAxios } from "@utils/login";
import { supabase } from "@utils/supabaseClient";
import { Oval } from "svg-loaders-react";

const Login = () => {
  const [email, setEmail] = useState();
  const [showInfo, setShowInfo] = useState(false);
  const [showActivateScreen, setShowActivateScreen] = useState(false);
  const [showDeletedScreen, setShowDeletedScreen] = useState(false);
  const [loading, setLoading] = useState(false);
  const [session, setSession] = useState(null);
  const [userProfile, setUserProfile] = useState(null);
  const router = useRouter();

  const toggleInfo = () => {
    setShowInfo(!showInfo);
  };

  const [errorMsg, setErrorMsg] = useState("");
  const [isApple, setIsApple] = useState(false);
  const [token, setToken] = useState();
  // const [userID, setUserID] = useState();

  useEffect(() => {
    supabase.auth.getSession().then(({ data: { session } }) => {
      setSession(session);
    });

    supabase.auth.onAuthStateChange((_event, session) => {
      setSession(session);
    });
  }, []);

  useEffect(() => {
    let mounted = true;
    if (Capacitor.getPlatform() === "ios") {
      setIsApple(true);
    }
    getUserProfile();
    return function cleanup() {
      mounted = false;
    };
  }, [session]);

  /***** --- Debug  --- *****/

  // console.log("session");
  // console.log(session);

  // console.log("userProfile");
  // console.log(userProfile);

  // console.log("whiteLabelUser");
  // console.log(whiteLabelUser);
  // console.log(userGroups);

  // console.log(categories);

  /***** --- Debug  --- *****/

  async function getUserProfile() {
    try {
      setLoading(true);

      const {
        data: { user },
      } = await supabase.auth.getUser();

      let { data, error, status } = await supabase
        .from("profiles")
        .select("*")
        .eq("id", user.id)
        .single();

      if (error && status !== 406) {
        throw error;
      }

      if (data) {
        setUserProfile(data);
      }
    } catch (error) {
      // console.log("user not logged in");
    } finally {
      setLoading(false);
    }
  }

  async function deleteAccount() {
    const userDataToSend = {
      entry: userProfile.id,
    };
    try {
      setLoading(true);

      let { error } = await supabase
        .from("profiles")
        .delete()
        .match({ id: userProfile.id });

      if (error) {
        throw error;
      }
    } catch (error) {
      alert(error.message);
    } finally {
      try {
        setLoading(true);
        const { data, error } = await supabase.functions.invoke("delete-user", {
          body: JSON.stringify(userDataToSend),
        });

        if (error) alert(error);

        if (data) {
          if (data.error) {
            console.log(data.error);
            // setErrorOnSubmit(data.error);
          } else {
            // console.log(data);
          }
        }
      } catch (error) {
        console.log(error);
      } finally {
        // console.log("deleted? auth?");
        supabase.auth.signOut();
        router.push("/login");
      }
    }
  }

  return (
    <div className="flex flex-col h-screen">
      <Header iOS={isApple} toggleInfo={toggleInfo} info={true} />

      {showActivateScreen ? (
        <main className="flex-1 overflow-y-auto bg-gray-100 ">
          <div className="flex  flex-col justify-center py-12 sm:px-6 lg:px-8">
            <div className="sm:mx-auto sm:w-full sm:max-w-md">
              {/* <div className="mx-auto flex justify-center p-6">
          <img
            src="/img/logo.png"
            className="object-contain pb-1 h-12 w-full"
          />
        </div> */}
              <div>
                <div>
                  <h2 className="font-Montserrat text-center text-2xl font-extrabold text-gray-800">
                    Final confirmation
                  </h2>
                </div>
              </div>

              <div className="mt-6">
                <button
                  onClick={() => deleteAccount()}
                  className=" bg-red-500 w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-secondary focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                >
                  Confirm delete
                </button>
              </div>
            </div>

            {/* <div>
                <h2 className="font-Montserrat text-center text-2xl font-extrabold text-gray-800">
                  Account deleted
                </h2>
              </div>
            </div> */}

            {/* <div className="mt-8 sm:mx-auto sm:w-full sm:max-w-md m-6">
              <div className="bg-white py-8 px-4 shadow sm:rounded-lg sm:px-10">
                <div>
                  <p className="mb-3 font-PTSans">
                    As this is your first login to the new app we you need to
                    reset your password.
                  </p>

                  <p className="mb-3 font-PTSans">
                    We have sent you an email with a link that will let you do
                    this
                  </p>
                </div>
              </div>
            </div> */}
          </div>
        </main>
      ) : (
        <main className="flex-1 overflow-y-auto bg-gray-100 ">
          <DeleteForm user={userProfile} deleteFinal={deleteAccount} />
          {/* <DeleteForm isLogin errorMessage={errorMsg} onSubmit={handleSubmit} /> */}
        </main>
      )}

      <BottomNav name="account" />
    </div>
  );
};

export default Login;
