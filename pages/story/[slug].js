import { useState, useEffect } from "react";
import TopNav from "@components/ui/TopNav";
import InfoView from "@components/ui/Info";
import SlideShow from "@components/ui/SlideShow";
import { Capacitor } from "@capacitor/core";
import { supabase } from "@utils/supabaseClient";

export default function Story({ story, storyImages }) {
  const [userData, setUserData] = useState(null);
  const [session, setSession] = useState(null);
  const [loading, setLoading] = useState(false);

  // console.log("userData");
  // console.log(userData);

  // console.log("story");
  // console.log(story);

  // console.log("storyImages");
  // console.log(storyImages);

  useEffect(() => {
    supabase.auth.getSession().then(({ data: { session } }) => {
      setSession(session);
    });

    supabase.auth.onAuthStateChange((_event, session) => {
      setSession(session);
    });
  }, []);

  useEffect(() => {
    let mounted = true;
    // if (Capacitor.getPlatform() === "ios") {
    //   setIsApple(true);
    // }
    getUserProfile();
    return function cleanup() {
      mounted = false;
    };
  }, [session]);

  // useEffect(() => {
  //   let mounted = true;

  //   if (userData) {
  //     // trigger started_reading_story event
  //     console.log("found user");
  //   }

  //   // getUserProfile();
  //   return function cleanup() {
  //     mounted = false;
  //   };
  // }, [userData]);

  async function getUserProfile() {
    try {
      setLoading(true);

      const {
        data: { user },
      } = await supabase.auth.getUser();

      let { data, error, status } = await supabase
        .from("profiles")
        .select("*")
        .eq("id", user.id)
        .single();

      if (error && status !== 406) {
        throw error;
      }

      if (data) {
        setUserData(data);

        // console.log(data);
      }
    } catch (error) {
      // console.log("user not logged in");
    } finally {
      setLoading(false);
    }
  }

  // change the order
  const images =
    storyImages &&
    storyImages.sort(function (a, b) {
      var textA = a.image_id;
      var textB = b.image_id;
      return textA < textB ? -1 : textA > textB ? 1 : 0;
    });

  const [isApple, setIsApple] = useState(false);
  const thumbnail = story && story.thumbnail;
  const shopLink = story && story.bookshop_link;

  const [index, setIndex] = useState(0);
  const [showInfo, setShowInfo] = useState(false);
  const [showSlideShow, setSlideShow] = useState(false);

  const toggleInfo = () => {
    setShowInfo(!showInfo);
  };

  const toggleSlideshow = (index) => {
    setIndex(index);
    setSlideShow(!showSlideShow);
  };

  useEffect(() => {
    if (Capacitor.getPlatform() === "ios") {
      setIsApple(true);
    }
  }, []);

  useEffect(() => {
    if (Capacitor.getPlatform() === "ios") {
      setIsApple(true);
    }
  }, []);

  return (
    <>
      {showSlideShow ? (
        <SlideShow
          slides={images && images}
          title={story && story.name}
          IndexNum={index}
          toggleSlideshow={toggleSlideshow}
          iOS={isApple}
          shopLink={shopLink && shopLink}
          thumbnail={thumbnail && thumbnail}
          userData={userData}
          isEbook={false}
        />
      ) : (
        <div className="flex flex-col h-screen">
          {showInfo ? (
            <InfoView
              storyInfo={images && images}
              toggleInfo={toggleInfo}
              title={story && story.name}
              iOS={isApple}
            />
          ) : (
            <>
              <TopNav
                title={story && story.name}
                toggleInfo={toggleInfo}
                iOS={isApple}
              />
              <main className="flex-1 overflow-y-auto">
                <div className="pt-3 pl-3 pr-3">
                  <ul role="list" className="grid grid-cols-3 gap-x-3 gap-y-3">
                    {images &&
                      images.map((img, index) => (
                        <li key={index} className="relative">
                          <div
                            className="group block w-full aspect-w-10 rounded-md border shadow-lg overflow-hidden"
                            onClick={() => {
                              toggleSlideshow(index);
                            }}
                          >
                            <img
                              src={`data:image/jpeg;base64,${img.image_string}`}
                              alt=""
                              className="object-cover pointer-events-none group-hover:opacity-75"
                            />
                          </div>
                        </li>
                      ))}
                  </ul>
                </div>
              </main>
            </>
          )}
        </div>
      )}
    </>
  );
}

export async function getStaticPaths() {
  const { data, error } = await supabase.from("stories").select("*");

  const stories = data;

  const paths =
    stories &&
    stories.map((cat) => ({
      params: { slug: cat.slug },
    }));

  return {
    paths,
    fallback: false,
  };
}

export async function getStaticProps({ params }) {
  const { slug } = params;

  let story;
  let storyImages;
  let imageIds;

  try {
    const { data, error } = await supabase
      .from("stories")
      .select("*")
      .eq("slug", slug)
      .single();

    story = data;

    if (data) {
      // imageIds = data.images;

      if (data.images) {
        let testArray = JSON.parse(data.images);
        imageIds = testArray;
      }
    }
  } catch (error) {
    console.log(error);
  } finally {
    try {
      const { data, error } = await supabase
        .from("images")
        .select("*")
        .in("legacy_id", [imageIds]);
      // .textSearch("legacy_id", [tagId]);
      storyImages = data;

      if (data) {
        // console.log(data);
      }
    } catch (error) {
      console.log(error);
    } finally {
    }
  }

  return {
    props: { story, storyImages },
  };
}
