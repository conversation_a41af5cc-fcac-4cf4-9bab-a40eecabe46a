import { useState, useEffect } from "react";
import useUser from "@lib/useUser";
import Image from "next/image";
import Link from "next/link";
import BottomNav from "@components/ui/BottomNav";
import Header from "@components/ui/Header";
import { Capacitor } from "@capacitor/core";
import WelcomeView from "@components/ui/Welcome";
import { supabase } from "@utils/supabaseClient";
import { Oval } from "svg-loaders-react";
import { ChevronRightIcon } from "@heroicons/react/solid";

export default function Stories({ categories }) {
  // console.log(categories);
  // const { user, mutateUser } = useUser();
  const [session, setSession] = useState(null);
  const [userProfile, setUserProfile] = useState(null);
  const [whiteLabelUser, setWhiteLabelUser] = useState(null);

  const [showInfo, setShowInfo] = useState(false);
  const [isApple, setIsApple] = useState(false);
  const [loading, setLoading] = useState(false);
  const [userGroups, setUserGroups] = useState(true);

  // useEffect(() => {
  //   supabase.auth.getSession().then(({ data: { session } }) => {
  //     setSession(session);
  //   });

  //   supabase.auth.onAuthStateChange((_event, session) => {
  //     setSession(session);
  //   });
  // }, []);

  useEffect(() => {
    let mounted = true;

    if (Capacitor.getPlatform() === "ios") {
      setIsApple(true);
    }

    // getUserProfile();

    return function cleanup() {
      mounted = false;
    };
  }, [session]);

  /***** --- Debug  --- *****/

  // console.log("session");
  // console.log(session);

  // console.log("userProfile");
  // console.log(userProfile);

  // console.log("whiteLabelUser");
  // console.log(whiteLabelUser);
  // console.log(userGroups);

  // console.log(categories);

  /***** --- Debug  --- *****/

  async function getUserProfile() {
    try {
      setLoading(true);

      const {
        data: { user },
      } = await supabase.auth.getUser();

      let { data, error, status } = await supabase
        .from("profiles")
        .select("*")
        .eq("id", user.id)
        .single();

      if (error && status !== 406) {
        throw error;
      }

      if (data) {
        setUserProfile(data);
        // setWhiteLabelUser(data.active);

        // check date

        const userExpDate = data.white_label;
        // console.log("userExpDate");
        // console.log(userExpDate);

        const date = new Date();

        let day = date.getDate();
        let month = date.getMonth() + 1;
        let year = date.getFullYear();

        let currentDateTmp = `${year}-${month}-${day}`;
        const currentDate = currentDateTmp;

        // console.log("currentDate");
        // console.log(currentDate);

        const userExpDateParse = Date.parse(userExpDate);
        const currDateParse = Date.parse(currentDate);

        // if the user has white label access set the flag here (valid date)
        if (userExpDateParse < currDateParse) {
          setWhiteLabelUser(false);
        } else {
          setWhiteLabelUser(true);
        }
      }
    } catch (error) {
      // console.log("user not logged in");
    } finally {
      setLoading(false);
    }
  }

  const toggleInfo = () => {
    setShowInfo(!showInfo);
  };

  const categoriesByIndex = []
    .concat(categories)
    .sort((a, b) => (a.index > b.index ? 1 : -1))
    .map((cat, i) => (
      <li key={i}>
        <Link key={cat.index} href={"/category/" + cat.slug}>
         
            <div className="flex items-center px-4 py-4 sm:px-6">
              <div className="min-w-0 flex-1 flex items-center">
                <div className="flex-shrink-0">
                  {cat.images.image_string && (
                    <img
                      className="w-24 rounded-md"
                      src={`data:image/jpeg;base64,${cat.images.image_string}`}
                    />
                  )}
                </div>
                <div className="min-w-0 flex-1 px-4 md:grid md:grid-cols-2 md:gap-4">
                  <div>
                    <p className="font-medium text-greyDark opacity-90 ">
                      {cat.name}
                    </p>
                  </div>
                </div>
              </div>
              <div>
                <ChevronRightIcon
                  className="h-5 w-5 text-greyDark opacity-90"
                  aria-hidden="true"
                />
              </div>
            </div>
     
        </Link>
      </li>
    ));

  return (
    <>
      {loading ? (
        <div className="grid h-screen place-items-center">
          <div className="mt-10 mb-10 flex flex-row space-x-2">
            <Oval stroke="#0c39ac" />
          </div>
        </div>
      ) : (
        <div className="flex flex-col h-screen">
          {showInfo ? (
            <WelcomeView
              toggleInfo={toggleInfo}
              // title={category.name}
              iOS={isApple}
            />
          ) : (
            <>
              <Header iOS={isApple} toggleInfo={toggleInfo} info={true} />
              <main className="flex-1 overflow-y-auto">
                {/* <div className="bg-white shadow overflow-hidden sm:rounded-md"> */}
                <div>
                  <ul className="divide-y divide-gray-200">
                    {categories && <>{categoriesByIndex}</>}
                  </ul>
                </div>
              </main>

              <BottomNav name="stories" />
            </>
          )}
        </div>
      )}
    </>
  );
}

export async function getStaticProps() {
  const { data, error } = await supabase
    .from("categories")
    .select(
      `name, slug, legacy_id, bookshop_link, category_image, info, index, images(image_string), subcategories`
    );
  // console.log(data);

  const categories = data;

  return {
    props: { categories },
  };
}
