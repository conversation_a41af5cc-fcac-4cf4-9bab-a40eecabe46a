import React, { useState, useEffect } from "react";
import useUser from "@lib/useUser";
import fetchJson from "@lib/fetchJson";
import Header from "@components/ui/Header";
import BottomNav from "@components/ui/BottomNav";
import { Capacitor } from "@capacitor/core";
import Link from "next/link";
import ResetForm from "@components/ui/PassResetForm";
import { useRouter } from "next/router";
import Router from "next/router";

const Verify = () => {
  const [passwordIsChanged, setPasswordIsChanged] = useState(false);
  const [resetPass, setResetPass] = useState(true);
  const [email, setEmail] = useState();
  const [showInfo, setShowInfo] = useState(false);
  const [errorMsg, setErrorMsg] = useState("");

  const { query } = useRouter();

  if (query && query.id) {
    // handleVerify(query.id)
    checkUser(query.id);
  }

  const toggleInfo = () => {
    setShowInfo(!showInfo);
  };

  const { mutateUser } = useUser({
    redirectTo: "/",
    redirectIfFound: true,
  });

  const [isApple, setIsApple] = useState(false);

  useEffect(() => {
    if (Capacitor.getPlatform() === "ios") {
      setIsApple(true);
    }
  }, []);

  async function checkUser(userId) {
    const body = {
      id: userId,
    };

    try {
      await fetchJson("/api/getuser", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(body),
      }).then((res) => {
        // console.log(res.id);
        if (res.password_is_changed === false) {
          handleVerify(res.id);
          // show reset password
          setEmail(res.mail);
        } else {
          handleVerify(res.id);
          // redirect / show login screen
          Router.push("/login");
        }
      });
    } catch (error) {
      console.error("An unexpected error happened:", error);
      setErrorMsg(error.message);
    }
  }

  async function handleVerify(userId) {
    const body = {
      id: userId,
    };

    try {
      await fetchJson("/api/verify", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(body),
      }).then((res) => {
        setEmail(res.mail);
        // console.log(res);
      });
    } catch (error) {
      console.error("An unexpected error happened:", error);
      setErrorMsg(error.message);
    }
  }

  async function handlePassChange(e) {
    e.preventDefault();

    const body = {
      email: email,
      password: e.target.password.value,
    };

    try {
      await fetchJson("/api/reset", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(body),
      }).then((res) => {
        // console.log(res.message);
        if (res.message === "email not found") {
          setErrorMsg("email not found");
        } else {
          setResetPass(false);
        }
      });
    } catch (error) {
      console.error("An unexpected error happened:", error);
      setErrorMsg(error.message);
    }
  }

  return (
    <div className="flex flex-col h-screen">
      <Header iOS={isApple} toggleInfo={toggleInfo} info={true} />

      <main className="flex-1 overflow-y-auto bg-gray-100 ">
        {resetPass ? (
          <ResetForm
            errorMessage={errorMsg}
            email={email}
            onSubmit={handlePassChange}
          />
        ) : (
          <>
            <div className="p-3 bg-gray-100 flex flex-col justify-center py-12 sm:px-6 lg:px-8">
              <div className="sm:mx-auto sm:w-full sm:max-w-md">
                <div>
                  <h2 className="font-Montserrat mt-6 text-center text-2xl font-extrabold text-gray-800">
                    Your password has been reset
                  </h2>
                </div>
              </div>

              <div className="mt-8 sm:mx-auto sm:w-full sm:max-w-md m-6">
                <div className="bg-white py-8 px-4 shadow sm:rounded-lg sm:px-10">
                  <div>
                    <div className="mt-6">
                      <Link href="/login">
                        <button className=" bg-blue-800 w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-secondary focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                          Login
                        </button>
                      </Link>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </>
        )}
      </main>
      <BottomNav name="account" />
    </div>
  );
};

export default Verify;
