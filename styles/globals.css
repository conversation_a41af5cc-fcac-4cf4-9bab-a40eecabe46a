@tailwind base;
@tailwind components;
@tailwind utilities;

@font-face {
  font-family: "PT Sans";
  font-style: normal;
  font-weight: 400;
  font-display: swap;
  src: local("PT Sans"), url("../fonts/ptsans.woff2") format("woff2"),
    url("../fonts/ptsans.ttf") format("truetype");
  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA,
    U+02DC, U+2000-206F, U+2074, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215,
    U+FEFF, U+FFFD;
}

:root {
  --ion-color-primary: #2d519f;
  --ion-color-primary-rgb: 45, 81, 159;
  --ion-color-primary-contrast: #ffffff;
  --ion-color-primary-contrast-rgb: 255, 255, 255;
  --ion-color-primary-shade: #28478c;
  --ion-color-primary-tint: #4262a9;

  --ion-color-secondary: #43a6dd;
  --ion-color-secondary-rgb: 67, 166, 221;
  --ion-color-secondary-contrast: #000000;
  --ion-color-secondary-contrast-rgb: 0, 0, 0;
  --ion-color-secondary-shade: #3b92c2;
  --ion-color-secondary-tint: #56afe0;

  --ion-color-tertiary: #1b3161;
  --ion-color-tertiary-rgb: 27, 49, 97;
  --ion-color-tertiary-contrast: #ffffff;
  --ion-color-tertiary-contrast-rgb: 255, 255, 255;
  --ion-color-tertiary-shade: #182b55;
  --ion-color-tertiary-tint: #324671;

  --ion-color-success: #2dd36f;
  --ion-color-success-rgb: 45, 211, 111;
  --ion-color-success-contrast: #ffffff;
  --ion-color-success-contrast-rgb: 255, 255, 255;
  --ion-color-success-shade: #28ba62;
  --ion-color-success-tint: #42d77d;

  --ion-color-warning: #ffc409;
  --ion-color-warning-rgb: 255, 196, 9;
  --ion-color-warning-contrast: #000000;
  --ion-color-warning-contrast-rgb: 0, 0, 0;
  --ion-color-warning-shade: #e0ac08;
  --ion-color-warning-tint: #ffca22;

  --ion-color-danger: #eb445a;
  --ion-color-danger-rgb: 235, 68, 90;
  --ion-color-danger-contrast: #ffffff;
  --ion-color-danger-contrast-rgb: 255, 255, 255;
  --ion-color-danger-shade: #cf3c4f;
  --ion-color-danger-tint: #ed576b;

  --ion-color-dark: #222428;
  --ion-color-dark-rgb: 34, 36, 40;
  --ion-color-dark-contrast: #ffffff;
  --ion-color-dark-contrast-rgb: 255, 255, 255;
  --ion-color-dark-shade: #1e2023;
  --ion-color-dark-tint: #383a3e;

  --ion-color-medium: #92949c;
  --ion-color-medium-rgb: 146, 148, 156;
  --ion-color-medium-contrast: #ffffff;
  --ion-color-medium-contrast-rgb: 255, 255, 255;
  --ion-color-medium-shade: #808289;
  --ion-color-medium-tint: #9d9fa6;

  --ion-color-light: #f4f5f8;
  --ion-color-light-rgb: 244, 245, 248;
  --ion-color-light-contrast: #000000;
  --ion-color-light-contrast-rgb: 0, 0, 0;
  --ion-color-light-shade: #d7d8da;
  --ion-color-light-tint: #f5f6f9;

  --ion-safe-area-top: 15px;
  --ion-safe-area-bottom: 10px;
  --ion-padding: 25px;

  --ion-grid-padding: 20px;

  /* --ion-safe-area-right: 20px;
  --ion-safe-area-left: 20px; */
}

.ios {
  /* iphone SE */
  /* --ion-safe-area-top: 20px; */

  /* iphone 12 */
  --ion-safe-area-top: 42px;
  --ion-safe-area-bottom: 16px;
}

.ios .modal-custom {
  --ion-padding-top: 42px;
  --ion-safe-area-bottom: 16px;
}

/* .android {
  --ion-safe-area-top: 0px;
} */

.disabled {
  pointer-events: none;
  opacity: 0.4;
}

.custom-item {
  --border-radius: 7px;
}

ion-modal.fullscreen {
  --width: 100%;
  --height: 100%;
  --border-radius: 0;
}
