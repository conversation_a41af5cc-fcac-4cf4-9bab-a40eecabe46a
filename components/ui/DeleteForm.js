import React from "react";
import PropTypes from "prop-types";
import { useRouter } from "next/router";
import Image from "next/image";
import Link from "next/link";

const DeleteForm = ({ user, deleteFinal }) => {
  return (
    <div className="flex  flex-col justify-center py-12 sm:px-6 lg:px-8">
      <div className="sm:mx-auto sm:w-full sm:max-w-md">
        {/* <div className="mx-auto flex justify-center p-6">
          <img
            src="/img/logo.png"
            className="object-contain pb-1 h-12 w-full"
          />
        </div> */}

        <div>
          <h2 className="font-Montserrat text-center text-2xl font-extrabold text-gray-800">
            Account will be deleted
          </h2>
        </div>
      </div>

      <div className="mt-8 sm:mx-auto sm:w-full sm:max-w-md m-6">
        <div className="bg-white py-8 px-4 shadow sm:rounded-lg sm:px-10">
          <div>
            <p className="mb-3 font-PTSans text-xl">Proceed with caution</p>
            <p className="mb-3 font-PTSans">
              The account {user && user.email} will be{" "}
              <u>permanently deleted</u> and can't be undone.
            </p>

            <p className="mb-3 font-PTSans"></p>
          </div>

          <div className="mt-6">
            <button
              onClick={() => deleteFinal()}
              className=" bg-red-500 w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-secondary focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
            >
              Confirm account
            </button>
          </div>

          {/* <div className="flex flex-row">
          {errorMessage && (
            <p className="mt-3 text-error">Incorrect login details</p>
          )}
        </div> */}
        </div>
      </div>
    </div>
  );
};

export default DeleteForm;
