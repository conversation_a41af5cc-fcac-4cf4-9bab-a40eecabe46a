import Styles from "../../styles/BottomNav.module.css";
import { useState } from "react";
import { useRouter } from "next/router";
import {
  IoBook,
  IoBookOutline,
  IoCart,
  IoCartOutline,
  IoPerson,
  IoPersonOutline,
  IoLibrary,
  IoLibraryOutline,
} from "react-icons/io5";

const BottomNav = (props) => {
  const router = useRouter();
  const [activeTabs, setActiveTabs] = useState(props.name);

  return (
    <div className={`${Styles.bottomNav}`}>
      <div className={`${Styles.bnTab}`}>
        {activeTabs === "stories" ? (
          <IoBook
            size="30"
            color="#2d519f"
            onClick={() => setActiveTabs("stories")}
          />
        ) : (
          <IoBookOutline
            size="30"
            color="#000"
            onClick={() => setActiveTabs("stories", router.push("/"))}
          />
        )}
      </div>

      <div className={`${Styles.bnTab}`}>
        {activeTabs === "ebooks" ? (
          <IoLibrary
            size="30"
            color="#2d519f"
            onClick={() => setActiveTabs("ebooks")}
          />
        ) : (
          <IoLibraryOutline
            size="30"
            color="#000"
            onClick={() => setActiveTabs("ebooks", router.push("/ebooks"))}
          />
        )}
      </div>

      <div className={`${Styles.bnTab}`}>
        {activeTabs === "bookshop" ? (
          <IoCart
            size="30"
            color="#2d519f"
            onClick={() => setActiveTabs("bookshop")}
          />
        ) : (
          <IoCartOutline
            size="30"
            color="#000"
            onClick={() => setActiveTabs("bookshop", router.push("/bookshop"))}
          />
        )}
      </div>
      <div className={`${Styles.bnTab}`}>
        {activeTabs === "account" ? (
          <IoPerson
            size="30"
            color="#2d519f"
            onClick={() => setActiveTabs("account")}
          />
        ) : (
          <IoPersonOutline
            size="30"
            color="#000"
            onClick={() => setActiveTabs("account", router.push("/account"))}
          />
        )}
      </div>
    </div>
  );
};

export default BottomNav;
