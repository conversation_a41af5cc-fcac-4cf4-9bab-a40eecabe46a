import {
  IoSearch,
  IoInformationCircle,
  IoChevronBackOutline,
} from "react-icons/io5";
import { Popover } from "@headlessui/react";
import { SearchIcon } from "@heroicons/react/solid";

import { XIcon } from "@heroicons/react/outline";
import Link from "next/link";

function classNames(...classes) {
  return classes.filter(Boolean).join(" ");
}

const Header = ({ iOS, toggleInfo, info }) => {
  const goBack = () => {
    window.history.back();
  };

  return (
    <>
      <Popover
        as="header"
        className={({ open }) =>
          classNames(
            open ? "fixed inset-0 z-40 overflow-y-auto" : "",
            "bg-white shadow-sm lg:static lg:overflow-y-visible"
          )
        }
      >
        {({ open }) => (
          <>
            <div
              className={
                iOS
                  ? "max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 shadow-md pt-8"
                  : "mx-auto px-2 sm:px-6 lg:px-8 shadow-md"
              }
            >
              <div className="relative flex justify-between xl:grid xl:grid-cols-12 lg:gap-8">
                <div className="flex md:absolute md:left-0 md:inset-y-0 lg:static xl:col-span-2">
                  <div className="flex-shrink-0 flex items-center">
                    {info ? (
                      <IoInformationCircle
                        size="30"
                        color="#2D519F"
                        onClick={toggleInfo}
                      />
                    ) : (
                      <div className="flex pl-1">
                        <IoChevronBackOutline
                          size="30"
                          color="#2D519F"
                          onClick={goBack}
                        />
                      </div>
                    )}
                  </div>
                </div>
                <div className="min-w-0 flex-1 md:px-8 lg:px-0 xl:col-span-7">
                  <div className="flex items-center px-6 py-4 md:max-w-3xl md:mx-auto lg:max-w-none lg:mx-0 xl:px-0">
                    <div className="w-full">
                      <div className="flex items-center">
                        <img
                          src="/img/logo.png"
                          className="object-contain pb-1 h-12 w-full"
                        />
                      </div>
                    </div>
                  </div>
                </div>
                <div className="flex items-center md:absolute md:right-0 md:inset-y-0 ">
                  <Link href="/search">
                    <div>
                      <IoSearch size="24" color="#2D519F" />
                    </div>
                  </Link>
                </div>
              </div>
            </div>

            <Popover.Panel as="nav" className="lg:hidden" aria-label="Global">
              <div className="pl-10 pr-10 pt-6">
                <label htmlFor="search" className="sr-only">
                  Search
                </label>
                <div className="relative">
                  <div className="pointer-events-none absolute inset-y-0 left-0 pl-3 flex items-center">
                    <SearchIcon
                      className="h-5 w-5 text-gray-400"
                      aria-hidden="true"
                    />
                  </div>
                  <input
                    id="search"
                    name="search"
                    className="block w-full bg-white border border-gray-300 rounded-md py-2 pl-10 pr-3 text-sm placeholder-gray-500 focus:outline-none focus:text-gray-900 focus:placeholder-gray-400 focus:ring-1 focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                    placeholder="Search"
                    type="search"
                  />
                </div>
              </div>
            </Popover.Panel>
          </>
        )}
      </Popover>
    </>
  );
};

export default Header;
