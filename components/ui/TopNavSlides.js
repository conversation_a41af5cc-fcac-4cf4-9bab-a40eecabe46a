import { Disclosure } from "@headlessui/react";
import {
  IoChevronBackOutline,
  IoChevronForwardOutline,
  IoSearch,
  IoInformationCircle,
  IoChatboxEllipsesOutline,
  IoChatboxEllipses,
  IoArrowBack,
  IoArrowForward,
  IoArrowForwardCircle,
  IoArrowBackCircle,
  IoArrowBackCircleOutline,
  IoArrowForwardCircleOutline,
} from "react-icons/io5";

import { BsArrowLeftSquareFill, BsArrowRightSquareFill } from "react-icons/bs";

import {
  TbArrowBigLeft,
  TbArrowBigRight,
  TbArrowBigLeftFilled,
  TbArrowBigRightFilled,
} from "react-icons/tb";

const TopNavSlides = ({
  title,
  toggleSlideshow,
  toggleTheInfo,
  iOS,
  showNext,
  showPrevious,
}) => {
  // console.log(showNext);

  return (
    <Disclosure as="nav" className="bg-white  pt-3">
      <>
        <div
          className={
            iOS
              ? "mx-auto px-2 sm:px-6 lg:px-8 pt-8"
              : "mx-auto px-2 sm:px-6 lg:px-8 "
          }
        >
          <div className="relative flex justify-between h-12">
            <div className="flex-1 flex items-center justify-center">
              <div className="flex pl-1" onClick={toggleSlideshow}>
                <IoChevronBackOutline size="30" color="#2D519F" />
                <div className="text-md pt-0.5 text-primary">Back</div>
              </div>
              <div className="flex-1 font-PTSans text-lg text-center">
                {title}
              </div>
              <div className="flex pr-1">
                <div className="pr-2">
                  <TbArrowBigLeftFilled
                    onClick={showPrevious}
                    size="25"
                    color="#2D519F"
                  />
                </div>
                {/* <div className="text-md pt-0 pl-2 pr-2 text-primary">
                  Change page
                </div> */}
                {/* <IoChatboxEllipses
                  onClick={toggleTheInfo}
                  size="30"
                  color="#2D519F"
                /> */}
                <TbArrowBigRightFilled
                  onClick={showNext}
                  size="25"
                  color="#2D519F"
                />
              </div>
            </div>
          </div>
        </div>
      </>
    </Disclosure>
  );
};

export default TopNavSlides;
