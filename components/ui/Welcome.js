import Card from "./Card";
import Link from "next/link";
import Image from "next/image";
import { SearchIcon, ShoppingCartIcon } from "@heroicons/react/solid";
import { Disclosure } from "@headlessui/react";
import {
  IoInformationCircle,
  IoLibrary,
  IoBook,
  IoGlobeOutline,
} from "react-icons/io5";

const WelcomeView = ({ info, toggleInfo, title, storyInfo, iOS }) => {
  return (
    <div>
      <Disclosure as="nav" className="bg-white shadow ">
        <>
          <div
            className={
              iOS
                ? "max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 shadow-md pt-8"
                : "mx-auto px-2 sm:px-6 lg:px-8 shadow-md"
            }
          >
            <div className="relative flex justify-between xl:grid xl:grid-cols-12 lg:gap-8">
              <div className="flex md:absolute md:left-0 md:inset-y-0 lg:static xl:col-span-2">
                <div className="flex-shrink-0 flex items-center pr-2">
                  <IoInformationCircle
                    size="30"
                    color="#2D519F"
                    onClick={toggleInfo}
                  />
                </div>
              </div>
              <div className="min-w-0 flex-1 md:px-8 lg:px-0 xl:col-span-6">
                <div className="flex items-center px-4 py-4 md:max-w-3xl md:mx-auto lg:max-w-none lg:mx-0 xl:px-0">
                  <div className="w-full">
                    <div className="flex items-center">
                      <img
                        src="/img/logo.png"
                        className="object-contain pb-1 pl-0 pr-0 h-12 w-full"
                      />
                    </div>
                  </div>
                </div>
              </div>
              <div className="flex items-center md:absolute md:right-0 md:inset-y-0 lg:hidden pl-1">
                <IoInformationCircle size="30" color="#ffffff" />
              </div>
            </div>
          </div>
        </>
      </Disclosure>
      {storyInfo ? (
        <div>
          <ul className="divide-y divide-gray-200">
            {storyInfo &&
              storyInfo.map((cat, index) => (
                <li key={cat.name}>
                  {/* <Link key={index} href={"/category/" + cat.slug}>
                    <a> */}
                  <div className="flex items-center px-4 py-4 sm:px-6">
                    <div className="min-w-0 flex-1 flex items-center">
                      <div className="flex-shrink-0">
                        {cat.image_string && (
                          <img
                            className="w-24 rounded-md"
                            src={`data:image/jpeg;base64,${cat.image_string}`}
                          />
                        )}
                      </div>
                      <div className="min-w-0 flex-1 px-4 md:grid md:grid-cols-2 md:gap-4">
                        <div>
                          <p className="font-medium text-greyDark opacity-90 ">
                            {cat.info}
                          </p>
                        </div>
                      </div>
                    </div>
                  </div>
                </li>
              ))}
          </ul>
        </div>
      ) : (
        <>
          <main className="flex-1 overflow-y-auto">
            {/* <div
              className="pt-12 p-4 text-lg space-y-6"
              dangerouslySetInnerHTML={{
                __html: info,
              }}
            /> */}

            <div className="pt-6 px-6 py-6 bg-white">
              <div className=" font-PTSans">
                <p className="text-xl text-primary">
                  Welcome to the BW Story App!
                </p>

                <p className=" pt-2 pb-2">
                  Here, you can browse over 450 short story extracts taken from
                  titles in the Books Beyond Words series.
                </p>

                <p className="pt-3 text-xl text-primary">Getting started</p>

                <div className="flex items-center">
                  <div className="p-3">
                    <SearchIcon
                      className="h-5 w-5 text-primary"
                      aria-hidden="true"
                    />
                  </div>
                  <div>
                    <p className=" pt-2 pb-2">
                      Tap on one of the nine categories in the list or, if you
                      know what you’re looking for, use the search icon.
                    </p>
                  </div>
                </div>

                <div className="flex items-center">
                  <div className="p-3">
                    <IoInformationCircle size="20" color="#2D519F" />
                  </div>
                  <div>
                    <p className=" pt-2 pb-2">
                      New to Books Beyond Words? You can find guidance on how to
                      read our wordless stories below.
                    </p>
                  </div>
                </div>

                {/* <div className="mx-auto flex justify-center">
                  <img
                    className="h-42 pl-0 pr-0 pt-2 pb-2"
                    src="/img/shop.jpg"
                  />
                </div> */}

                <p className="pl-11 pt-2 pb-2">
                  You will see the ‘i’ button in other places, too. It’s worth
                  having a look! You will find useful topic-related info as well
                  as possible storyline prompts.
                </p>

                <div className="flex items-center">
                  <div className="p-3">
                    <ShoppingCartIcon
                      className="h-5 w-5 text-primary"
                      aria-hidden="true"
                    />
                  </div>
                  <div>
                    <p className=" pt-2 pb-2">
                      Don’t forget, you can also visit our online bookshop by
                      tapping on the shopping cart icon.
                    </p>
                  </div>
                </div>

                <p className="pt-3 text-xl text-primary">
                  How to read the stories
                </p>

                <div className="flex items-center">
                  <div className="p-0">
                    {/* <IoBook size="20" color="#2D519F" /> */}
                  </div>
                  <div>
                    <p className=" pt-2 pb-2">
                      These stories are for people who find pictures easier to
                      understand than words. It is not necessary to be able to
                      read any words at all.
                    </p>
                  </div>
                </div>

                <div className="flex items-center">
                  <div className="p-3"></div>
                  <div>
                    <ol className="list-decimal">
                      <li className="p-1">
                        Start at the beginning and read the story in each
                        picture. Encourage the reader to move through the pages
                        at their own pace.
                      </li>
                      <li className="p-1">
                        Whether you are reading the story with one person or
                        with a group, encourage them to tell the story in their
                        own words. You will discover what each person thinks is
                        happening, what they already know, and how they feel.
                        You may think something different is happening in the
                        pictures yourself, but that doesn’t matter. Wait to see
                        if their ideas change as the story develops. Don’t
                        challenge the reader(s) or suggest their ideas are
                        wrong. Watch, wait and wonder.
                      </li>
                      <li className="p-1">
                        Some pictures may be more difficult to understand. It
                        can help to prompt the people you are supporting, for
                        example:
                      </li>

                      <ul className="list-disc pl-6">
                        <li className="pt-1">I wonder who that is?</li>
                        <li className="pt-1">I wonder what is happening? </li>
                        <li className="pt-1">What is he or she doing now?</li>
                        <li className="pt-1">
                          I wonder how he or she is feeling?
                        </li>
                        <li className="pt-1">
                          Do you feel like that? Has it happened to you/ your
                          friend/ your family?
                        </li>
                      </ul>
                      <li className="pt-3 p-1">
                        Some people will not be able to follow the story, but
                        they may be able to understand some of the pictures.
                        Stay longer with the pictures that interest them.
                      </li>
                    </ol>
                  </div>
                </div>

                <div className="flex items-center">
                  <div className="p-3">
                    <IoGlobeOutline
                      className="h-5 w-5 text-primary"
                      aria-hidden="true"
                    />
                  </div>
                  <div>
                    <p className=" pt-2 pb-2">
                      For more information about Books Beyond Words, visit our
                      website:
                    </p>
                    <p className="text-primary">
                      <a target="_blank" href="https://booksbeyondwords.co.uk/">
                        www.booksbeyondwords.co.uk
                      </a>
                    </p>
                    <p className="text-sm pt-4">
                      All text and illustrations in this app <br /> © Books
                      Beyond Words, 2024.
                    </p>
                  </div>
                </div>

                <p className="pt-3 text-xl text-primary">Licensed users</p>

                <div className="flex items-center">
                  <div className="p-3">
                    <IoLibrary size="20" color="#2D519F" />
                  </div>
                  <div>
                    <p className=" pt-2 pb-2">
                      If you or your organisation has a licence agreement with
                      us, you will also have access to the ebookshelf feature
                      where you can read whole titles from the Books Beyond
                      Words series.{" "}
                      <span className="font-bold">Happy storytelling!</span>
                    </p>
                  </div>
                </div>

                <div className="mt-3">
                  <button
                    onClick={toggleInfo}
                    className=" bg-blue-800 w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-secondary focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                  >
                    Get started
                  </button>
                </div>
              </div>
            </div>
          </main>
        </>
      )}
    </div>
  );
};

export default WelcomeView;
