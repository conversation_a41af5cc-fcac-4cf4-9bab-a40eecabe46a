import React from "react";
import { useState } from "react";
import { supabase } from "@utils/supabaseClient";
import { Oval } from "svg-loaders-react";
import Image from "next/image";
import Link from "next/link";
import { useRouter } from "next/router";

export default function LoginForm() {
  const [loading, setLoading] = useState(false);
  const [email, setEmail] = useState(null);
  // const [password, setPassword] = useState("");
  const [passCode, setPassCode] = useState("");
  const [showMesage, setshowMesage] = useState(false);
  const [errorOnSubmit, setErrorOnSubmit] = useState(false);

  const router = useRouter();

  // const handleLogin = async (email, password) => {
  //   try {
  //     setLoading(true);
  //     const { error } = await supabase.auth.signInWithPassword({
  //       email: email,
  //       password: password,
  //     });
  //     if (error) {
  //       throw error;
  //     } else {
  //       router.push("/");
  //     }
  //   } catch (error) {
  //     console.log(error.error_description || error.message);
  //     setErrorOnSubmit(error.message);
  //   } finally {
  //     setLoading(false);
  //   }
  // };

  const handleLogin = async (email) => {
    try {
      setLoading(true);
      const { data, error } = await supabase.auth.signInWithOtp({
        email: email,
        options: {
          // set this to false if you do not want the user to be automatically signed up
          shouldCreateUser: false,
        },
      });
      if (error) {
        throw error;
      } else {
        // console.log(data);
        setshowMesage(true);
        // router.push("/");
      }
    } catch (error) {
      // console.log(error.error_description || error.message);
      // setErrorOnSubmit(error.error_description || error.message);
    } finally {
      setLoading(false);
    }
  };

  const sendPasscode = async () => {
    try {
      setLoading(true);
      const {
        data: { session },
        error,
      } = await supabase.auth.verifyOtp({
        email: email,
        token: passCode,
        type: "email",
      });
      if (error) {
        throw error;
      } else {
        // console.log(session);
      }
    } catch (error) {
      // console.log(error.error_description || error.message);
      setErrorOnSubmit(error.message);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="flex flex-col justify-center py-3 sm:px-6 lg:px-8">
      <div className="sm:mx-auto sm:w-full sm:max-w-md">
        <h2 className="font-Ubuntu mt-6 text-center text-2xl font-extrabold text-gray-900">
          Sign into your account
        </h2>
      </div>

      <div className="mt-8 sm:mx-auto sm:w-full sm:max-w-md m-6">
        <div className="bg-white py-12 px-4 shadow rounded-lg sm:px-10">
          {loading ? (
            <div className="grid place-items-center">
              <div className="mt-10 mb-10 flex flex-row space-x-2">
                <Oval stroke="#0c39ac" />
              </div>
            </div>
          ) : (
            <div>
              {showMesage ? (
                <div className="grid place-items-center">
                  <div className="mt-10 mb-10 flex flex-row space-x-2">
                    <p>
                      Please check your email for a passcode and enter it here
                      to login
                    </p>
                  </div>

                  <div className="max-w-sm min-w-[20px]">
                    <div className="relative">
                      <input
                        value={passCode || ""}
                        onChange={(e) => setPassCode(e.target.value)}
                        type="passcode"
                        maxLength={6}
                        required
                        className="w-full bg-transparent placeholder:text-slate-400 text-slate-700 text-lg border border-slate-200 rounded-md pl-3 py-2 transition duration-300 ease focus:outline-none focus:border-slate-400 hover:border-slate-300 shadow-sm focus:shadow"
                        placeholder="passcode"
                      />
                      <button
                        className="absolute right-2 top-2 rounded bg-green-500 py-1 px-2.5 border border-transparent text-center text-sm text-white transition-all shadow-sm hover:shadow focus:bg-green-500 focus:shadow-none active:bg-green-700 hover:bg-green-700 active:shadow-none disabled:pointer-events-none disabled:opacity-50 disabled:shadow-none"
                        type="button"
                        onClick={sendPasscode}
                      >
                        Sign in
                      </button>
                    </div>
                  </div>
                  {errorOnSubmit ? (
                    <div className="text-error pt-6">{errorOnSubmit}</div>
                  ) : null}
                </div>
              ) : (
                <div>
                  {" "}
                  <div>
                    <label
                      htmlFor="email"
                      className="block text-sm font-medium text-gray-700"
                    >
                      Email address
                    </label>
                    <div className="mt-1">
                      <input
                        type="email"
                        placeholder="Your email"
                        value={email || ""}
                        onChange={(e) => setEmail(e.target.value)}
                        required
                        className="appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-primary focus:border-primary sm:text-sm"
                      />
                    </div>
                    {/* <label
                      htmlFor="email"
                      className="block text-sm font-medium text-gray-700 mt-3"
                    >
                      Password
                    </label>
                    <div className="mt-1">
                      <input
                        type="password"
                        placeholder="Password"
                        value={password}
                        onChange={(e) => setPassword(e.target.value)}
                        required
                        className="appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-primary focus:border-primary sm:text-sm"
                      />
                    </div> */}
                  </div>
                  <div className="mt-6">
                    <button
                      onClick={(e) => {
                        e.preventDefault();
                        handleLogin(email);
                      }}
                      disabled={loading}
                      className=" bg-blue-800 w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-secondary focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                    >
                      Sign in
                    </button>
                    <div className="mt-0 flex flex-row space-x-2">
                      {/* <Link href="/register">
                        <button className="mt-3 w-full bg-green-500 flex justify-center py-2 px-5 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-secondary focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                          Register
                        </button>
                      </Link> */}
                      {/* <Link href="/reset-request">
                        <button className="mt-3 w-full bg-gray-500 flex justify-center py-2 px-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-secondary focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                          Reset password
                        </button>
                      </Link> */}
                    </div>

                    <div className="mt-0 flex flex-row space-x-2">
                      {/* {!errorOnSubmit ? (
                        <div className="text-error pt-6">{errorMessage}</div>
                      ) : null} */}
                    </div>
                  </div>
                </div>
              )}
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
