import { Disclosure } from "@headlessui/react";
import {
  IoChevronBackOutline,
  IoSearch,
  IoInformationCircle,
  IoHome,
} from "react-icons/io5";
import Link from "next/link";

const TopNav = ({ title, toggleInfo, iOS }) => {
  const goBack = () => {
    window.history.back();
  };

  return (
    <Disclosure as="nav" className="bg-white shadow pt-1">
      <>
        <div
          className={
            iOS
              ? "mx-auto px-2 sm:px-6 lg:px-8 shadow-md pt-8"
              : "mx-auto px-2 sm:px-6 lg:px-8 shadow-md"
          }
        >
          <div className="relative flex justify-between h-16">
            <div className="flex-1 flex items-center justify-center">
              <div className="flex pl-1" onClick={goBack}>
                <IoChevronBackOutline size="30" color="#2D519F" />
              </div>
              <Link href={"/"}>
                <div className="flex pl-3">
                  <IoHome size="25" color="#2D519F" onClick={toggleInfo} />
                </div>
              </Link>
              <div className="flex-1 font-PTSans text-lg text-center">
                {title}
              </div>
              <div className="flex pr-6">
                <IoInformationCircle
                  onClick={toggleInfo}
                  size="30"
                  color="#2D519F"
                />
              </div>
            </div>
          </div>
        </div>
      </>
    </Disclosure>
  );
};

export default TopNav;
