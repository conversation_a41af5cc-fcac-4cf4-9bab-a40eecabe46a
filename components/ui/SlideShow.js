import { useState, useEffect } from "react";
import Card from "./Card";
import Link from "next/link";
import Image from "next/image";
import { Disclosure } from "@headlessui/react";
import { IoChevronBackOutline, IoCloseCircleSharp } from "react-icons/io5";
import TopNavSlides from "../../components/ui/TopNavSlides";
import SwipeableViews from "react-swipeable-views";
import { virtualize, bindKeyboard } from "react-swipeable-views-utils";
import { supabase } from "@utils/supabaseClient";

const VirtualizeSwipeableViews = bindKeyboard(virtualize(SwipeableViews));

const SlideShow = ({
  slides,
  name,
  IndexNum,
  toggleSlideshow,
  iOS,
  shopLink,
  thumbnail,
  userData,
  title,
  isEbook,
}) => {
  const [toggleNav, setToggleNav] = useState(true);
  const [toggleInfo, setToggleInfo] = useState(false);
  const [index, setIndex] = useState(IndexNum);
  const [loading, setLoading] = useState(true);

  const slideTotal = slides.length + 1;

  // console.log("userData");
  // console.log(userData);

  // console.log("title");
  // console.log(title);

  const toggleTheNav = () => {
    setToggleNav(!toggleNav);
  };

  const toggleTheInfo = () => {
    setToggleInfo(!toggleInfo);
  };

  const handleChangeIndex = (index) => {
    setIndex(index);
  };

  const showNext = () => {
    if (index !== slideTotal - 1) {
      setIndex(index + 1);
      // set the icon colour to grey at end
    }
  };

  const showPrevious = () => {
    if (index !== 0) {
      setIndex(index - 1);
      // set the icon colour to grey at begining
    }
  };

  useEffect(() => {
    if (index === slideTotal - 1) {
      // console.log("reached the end");
      // run the event completed_reading_story_
      addUserEvent();
    }

    let mounted = true;

    return function cleanup() {
      mounted = false;
    };
  }, [index]);

  const slideShowItems = slides.map((slide, index) => {
    // if (index === slideTotal - 1) {
    //   return <>{finalSlide}</>;
    // }

    return (
      <div key={slide.id}>
        <img
          src={`data:image/jpeg;base64,${slide.image_string}`}
          alt=""
          className="max-h-[38rem]"
          // className="object-cover pointer-events-none group-hover:opacity-75"
        />
        {toggleInfo && <div className="p-2">{slide.info}</div>}
      </div>
    );
  });

  const slideRenderer = (params) => {
    const { index, key } = params;

    if (index === slideTotal - 1) {
      return (
        <div className="flex flex-col h-screen" key="finalSlide">
          <main className="flex-1 overflow-y-auto">
            <Card className="my-3 p-6 mx-auto">
              <div className="pt-6 px-6 my-1.5 font-PTSans bg-white rounded-xl">
                {/* <div className="text-xl">
                  These images are taken from the Books Beyond Words series
                </div> */}
                {isEbook ? (
                  <div className="text-xl">
                    To buy this or any of our books please visit our website.
                  </div>
                ) : (
                  <div className="text-xl">
                    These images are an extract from a Beyond Words book. To buy
                    all or any of our books please visit our website
                  </div>
                )}

                {/* <div>
                  <img
                    src={`data:image/jpeg;base64,${thumbnail}`}
                    alt=""
                    className="object-cover pointer-events-none group-hover:opacity-75"
                  />
                </div> */}
                <div>
                  {/* <h3 className="pt-3">
                    Visit our web store to purchase this and others
                  </h3> */}

                  {/* <h3 className="pt-3">
                    To read the whole story subscribe to the app or purchase a
                    paperback copy of this and other titles from our web store.
                  </h3> */}
                </div>
              </div>
              <div className="p-6 pt-3">
                <a target="_blank" href={shopLink}>
                  <button
                    onClick={() => addUserEventBuyBook()}
                    className=" bg-blue-800 w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-secondary focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                  >
                    Buy this book
                  </button>
                </a>
              </div>
            </Card>
          </main>
        </div>
      );
    } else {
      return <div key={key}>{slideShowItems[index]}</div>;
    }
  };

  // Add user started_reading_story

  async function addUserEvent() {
    try {
      setLoading(true);

      const dataEnter = {
        user_id: userData.id,
        event_type: "completed_reading_story_" + title,
        organisation: userData.organisation,
        profession: userData.profession,
        country: userData.country,
      };

      const { data, error } = await supabase
        .from("events")
        .insert(dataEnter)
        .select();

      if (error) {
        console.log(error);
        throw error;
      }
    } catch (error) {
      console.log(error.message);
    } finally {
      // trigger created_account event
      setLoading(false);
    }
  }

  // Add user clicked on buy this book

  async function addUserEventBuyBook() {
    // console.log("event log - buy this book");
    try {
      setLoading(true);

      const dataEnter = {
        user_id: userData.id,
        event_type: "visit_buy_book_" + title,
        organisation: userData.organisation,
        profession: userData.profession,
        country: userData.country,
      };

      const { data, error } = await supabase
        .from("events")
        .insert(dataEnter)
        .select();

      if (error) {
        console.log(error);
        throw error;
      }
    } catch (error) {
      console.log(error.message);
    } finally {
      // trigger created_account event
      setLoading(false);
    }
  }

  return (
    <>
      {toggleNav ? (
        <TopNavSlides
          title={name}
          toggleSlideshow={toggleSlideshow}
          toggleTheInfo={toggleTheInfo}
          iOS={iOS}
          showNext={showNext}
          showPrevious={showPrevious}
        />
      ) : (
        <>
          {!iOS ? (
            <div className="mt-12 pt-3"></div>
          ) : (
            <div className="mt-12 pt-11"></div>
          )}
        </>
      )}
      <VirtualizeSwipeableViews
        enableMouseEvents={false}
        resistance
        index={index}
        onChangeIndex={handleChangeIndex}
        slideRenderer={slideRenderer}
        // disabled={slidesDisabled}
        slideCount={slideTotal}
        onClick={toggleTheNav}
      />
    </>
  );
};

export default SlideShow;
