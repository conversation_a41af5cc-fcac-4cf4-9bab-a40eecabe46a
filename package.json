{"name": "bw-story-app", "version": "0.0.1", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "export": "next export", "build-mobile-ios": "next build && next export && npx cap run ios", "resources": "cordova-res ios && cordova-res android && node scripts/resources.js"}, "dependencies": {"@capacitor/android": "^5.0.0", "@capacitor/core": "^5.0.0", "@capacitor/ios": "^5.0.0", "@capacitor/live-updates": "0.1.1-beta.4", "@headlessui/react": "^1.3.0", "@heroicons/react": "^1.0.2", "@ionic/react": "^7.5.2", "@ionic/react-router": "^7.5.2", "@supabase/supabase-js": "^2.38.0", "autoprefixer": "^10.2.5", "axios": "^0.21.1", "cordova-res": "^0.15.3", "cors": "^2.8.5", "next": "^13.5.6", "next-iron-session": "^4.2.0", "postcss": "^8.4.31", "react": "^18.2.0", "react-dom": "^18.2.0", "react-icons": "^4.2.0", "react-router-dom": "^6.17.0", "react-swipeable-views": "^0.14.0", "svg-loaders-react": "^2.2.1", "swr": "^0.5.6", "tailwindcss": "^3.3.3"}, "devDependencies": {"@capacitor/cli": "^5.0.0", "pullstate": "^1.22.0", "reselect": "^4.0.0"}}